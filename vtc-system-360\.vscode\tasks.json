{"version": "2.0.0", "tasks": [{"label": "Build Cache", "type": "shell", "command": "python ${workspaceFolder}/runtime/init_cache.py --build", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Validate <PERSON>", "type": "shell", "command": "python ${workspaceFolder}/runtime/init_cache.py --validate", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Unit Tests (Scenarios)", "type": "shell", "command": "python -m pytest ${workspaceFolder}/runtime/test_scenarios.py -v", "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Itinerary Tests", "type": "shell", "command": "python -m pytest ${workspaceFolder}/tests/test_itinerary.py -v", "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Env Discount Tests", "type": "shell", "command": "python -m pytest ${workspaceFolder}/tests/test_env_discount.py -v", "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Flags Detection Tests", "type": "shell", "command": "python -m pytest ${workspaceFolder}/tests/test_flags_detection.py -v", "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run All Tests", "type": "shell", "command": "python -m pytest ${workspaceFolder}/tests/ ${workspaceFolder}/runtime/ -v", "group": {"kind": "test", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Quote from JSON (offline)", "type": "shell", "command": "python app.py --in-json ${input:json} --lang ${input:lang} --out ${input:out} --out-format ${input:fmt}", "presentation": {"reveal": "always", "panel": "dedicated"}, "problemMatcher": [], "inputs": [{"id": "json", "type": "promptString", "description": "Ruta al extracted.json", "default": "extracted.json"}, {"id": "lang", "type": "pickString", "description": "Idioma resumen", "options": ["ES", "FR", "EN"], "default": "ES"}, {"id": "out", "type": "promptString", "description": "Archivo salida", "default": "cotizacion.json"}, {"id": "fmt", "type": "pickString", "description": "Formato salida", "options": ["json", "md"], "default": "json"}]}, {"label": "Run from extract.json", "type": "shell", "command": "python app.py --in-json extract.json --skip-llm --out quote.json", "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "VTC: Process with Gemini Local", "type": "shell", "command": "echo '${input:message}' | gemini-local --prompt 02-prompts/gemini-v2/extract-itinerary.md --cache 05-runtime/cached_context.json", "problemMatcher": [], "presentation": {"echo": false, "reveal": "silent", "panel": "shared"}}, {"label": "VTC: <PERSON><PERSON><PERSON><PERSON>", "type": "shell", "command": "python vtc-system-360/runtime/session_manager.py --show-last", "presentation": {"reveal": "always", "panel": "new"}}], "inputs": [{"id": "message", "type": "promptString", "description": "Mensaje del cliente"}]}