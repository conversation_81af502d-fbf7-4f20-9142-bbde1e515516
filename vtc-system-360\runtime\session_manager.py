import io
import sys
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

import json
import os
import argparse
from datetime import datetime
from pathlib import Path

class VTCSessionManager:
    """
    Gestiona la persistencia de sesión entre reinicios de VSCode
    """
    def __init__(self, session_dir="vtc-system-360/runtime/sessions"):
        self.session_dir = Path(session_dir)
        self.session_dir.mkdir(exist_ok=True)
        self.current_session = None
        
    def save_session(self, data):
        """Guarda el estado actual de la sesión"""
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_file = self.session_dir / f"session_{session_id}.json"
        
        session_data = {
            "id": session_id,
            "timestamp": datetime.now().isoformat(),
            "files_generated": data.get("files", []),
            "last_command": data.get("command", ""),
            "results": data.get("results", {}),
            "context": data.get("context", {})
        }
        
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        # Actualizar enlace a sesión actual
        current_link = self.session_dir / "current_session.json"
        with open(current_link, 'w') as f:
            json.dump({"current": str(session_file)}, f)
        
        return session_id
    
    def load_last_session(self):
        """Carga la última sesión guardada"""
        current_link = self.session_dir / "current_session.json"
        
        if not current_link.exists():
            return None
        
        with open(current_link) as f:
            current = json.load(f)
        
        session_file = Path(current["current"])
        if session_file.exists():
            with open(session_file) as f:
                return json.load(f)
        
        return None
    
    def get_session_summary(self):
        """Obtiene un resumen de la sesión actual"""
        session = self.load_last_session()
        
        if not session:
            return "No hay sesión activa"
        
        return f"""
        📋 SESIÓN ACTIVA: {session['id']}
        ⏰ Última actividad: {session['timestamp']}
        📁 Archivos generados: {len(session['files_generated'])}
        🔧 Último comando: {session['last_command']}
        """

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="VTC Session Manager CLI")
    parser.add_argument("--show-last", action="store_true", help="Muestra un resumen de la última sesión guardada.")
    parser.add_argument("--restore", action="store_true", help="Restaura la última sesión (actualmente solo muestra resumen).")
    args = parser.parse_args()

    if args.show_last or args.restore:
        manager = VTCSessionManager()
        summary = manager.get_session_summary()
        print(summary)