# tests/test_env_discount.py
import pytest
import os
import json

# Mock QuotingEngine para no depender de la implementación real
class MockQuotingEngine:
    def calculate_price(self, svc):
        # Simula un precio fijo para simplificar el test de descuento
        return {"precio_final_num": 100.0, "precio_final_calculado": "100€"}

# Monkeypatch para el QuotingEngine en app.py
@pytest.fixture(autouse=True)
def mock_quoting_engine(monkeypatch):
    monkeypatch.setattr('runtime.quoting_engine.QuotingEngine', MockQuotingEngine)

@pytest.fixture(autouse=True)
def cleanup_env_vars():
    # Limpiar variables de entorno después de cada test
    old_env = os.environ.copy()
    yield
    os.environ.clear()
    os.environ.update(old_env)

def get_package_discount_from_env():
    PACKAGE_DISCOUNT_STR = os.getenv("PACKAGE_DISCOUNT", "0.05")
    try:
        return max(0.0, min(0.15, float(PACKAGE_DISCOUNT_STR)))
    except:
        return 0.05

def test_package_discount_from_env_var():
    # Simular que PACKAGE_DISCOUNT es 0.10 (10%)
    os.environ["PACKAGE_DISCOUNT"] = "0.10"
    
    services = [
        {"type": "transfer"}, # Servicio 1
        {"type": "hourly"},  # Servicio 2
        {"type": "guia"}     # Servicio 3
    ]

    total_before_discount = 0.0
    for svc in services:
        quote = MockQuotingEngine().calculate_price(svc)
        total_before_discount += quote["precio_final_num"]

    package_discount_value = get_package_discount_from_env()
    if len(services) >= 3 and package_discount_value > 0:
        total_after_discount = round(total_before_discount * (1.0 - package_discount_value), 2)
        assert total_after_discount == round(total_before_discount * 0.90, 2) # Esperamos 10% de descuento
        assert package_discount_value == 0.10

def test_package_discount_default_value():
    # Asegurarse de que PACKAGE_DISCOUNT no está en el entorno
    if "PACKAGE_DISCOUNT" in os.environ:
        del os.environ["PACKAGE_DISCOUNT"]

    import app
    import importlib
    importlib.reload(app)

    services = [
        {"type": "transfer"}, # Servicio 1
        {"type": "hourly"},  # Servicio 2
        {"type": "guia"}     # Servicio 3
    ]

    total_before_discount = 0.0
    for svc in services:
        quote = MockQuotingEngine().calculate_price(svc)
        total_before_discount += quote["precio_final_num"]

    if len(services) >= 3 and app.PACKAGE_DISCOUNT > 0:
        total_after_discount = round(total_before_discount * (1.0 - app.PACKAGE_DISCOUNT), 2)
        assert total_after_discount == round(total_before_discount * 0.95, 2) # Esperamos 5% por defecto
        assert app.PACKAGE_DISCOUNT == 0.05