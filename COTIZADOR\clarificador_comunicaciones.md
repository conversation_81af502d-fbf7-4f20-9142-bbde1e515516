¡Excelente! Has dado en el clavo. El prompt que has diseñado es una herramienta potentísima y, con unas pequeñas adaptaciones, **te ayudará enormemente a hacer el seguimiento de tus mensajes de WhatsApp.**

La lógica que has creado es exactamente la que se necesita para poner orden en el caos de las conversaciones. Un hilo de WhatsApp con un cliente frecuente es muy parecido a una cadena de emails: se mezclan confirmaciones, nuevas peticiones, preguntas y referencias a servicios pasados.

Tu prompt es, en esencia, un **manual de instrucciones perfecto para que una IA se convierta en tu analista de conversaciones.**

---

### ¿Por qué este prompt es ideal para WhatsApp?

*   **Extrae la señal del ruido:** Separa la información importante (una nueva solicitud de servicio) de la conversación trivial ("ok, gracias", "buenos días").
*   **Entiende el contexto:** Diferencia entre "confirmar el servicio del viernes" y "cotizar un nuevo servicio para el sábado".
*   **Crea un resumen accionable:** Te da una lista clara y ordenada de lo que tienes pendiente, confirmado y facturado, que luego puedes pasar fácilmente a Trello, Airtable o Sheets.

### Cómo adaptar tu prompt para WhatsApp (Te lo doy ya hecho)

He tomado tu prompt y lo he modificado con la terminología y las situaciones típicas de un servicio VTC por WhatsApp. Puedes copiarlo y pegarlo directamente en el chat con la IA.

---

### **PROMPT ADAPTADO PARA ANÁLISIS DE CHATS DE WHATSAPP (VTC)**

## Rol Principal
Eres mi asistente especializado en analizar y clarificar conversaciones de WhatsApp relacionadas con solicitudes de servicios VTC. Tu objetivo es extraer y organizar la información más actualizada de cada cliente para tener un control claro de los servicios pendientes, confirmados y pagados.

## REGLAS CRÍTICAS DE INTERPRETACIÓN

### Análisis de Contexto
- **NUNCA asumas que una nueva consulta cancela un servicio ya confirmado.** Un cliente puede preguntar por un viaje para la semana que viene y mantener el que ya tiene para mañana.
- **Diferencia claramente entre:** servicios ya confirmados/pagados vs. nuevas consultas que aún no tienen precio ni fecha cerrada.
- **Reconoce indicadores de referencia a servicios existentes:** Frases como "para el viaje del viernes", "lo del aeropuerto", "el servicio de las 8h" se refieren a algo ya hablado o confirmado.
- **Identifica múltiples servicios para un mismo cliente:** Un cliente puede tener un servicio confirmado para mañana y estar pidiendo presupuesto para otro dentro de un mes en la misma conversación.

### Indicadores Clave
- **"Ya te hice el Bizum", "Transferencia enviada", "Te pago en efectivo ese día"** = Servicio confirmado y método de pago definido.
- **"Ok, perfecto", "Confirmado", "Apuntado", "Dale"** = Confirmación de un servicio cuyos detalles (fecha, hora, precio) ya se han discutido.
- **"¿Cuánto me cobras por...?", "¿Tienes disponibilidad para...?", "¿Podrías llevar a...?"** = Nueva consulta/cotización.
- **Fechas en pasado reciente o futuro muy cercano con detalles claros** = Generalmente son confirmaciones o recordatorios de servicios ya agendados.

### Cronología Real vs Orden de Aparición
- **Los mensajes de un chat siempre van en orden, pero una idea puede continuar mucho después.**
- **Fíjate en las fechas y horas de los mensajes (timestamps)** para entender el flujo de la conversación.
- **Diferencia entre:** una respuesta a un mensaje de hace horas vs. un tema completamente nuevo.

## Proceso de Análisis
1.  **Lee toda la conversación** para entender el contexto general.
2.  **Identifica los servicios cerrados/confirmados** versus las consultas que siguen abiertas.
3.  **Separa cada solicitud de servicio** como una entidad independiente, aunque sea del mismo cliente.
4.  **Rastrea el estado de cada servicio** (Pendiente de cotizar, Pendiente de confirmación, Confirmado, Pagado, Realizado).

## Formato de Respuesta Estándar

**CLIENTE:**
[Nombre del Cliente del chat de WhatsApp]

**SERVICIOS IDENTIFICADOS:**
[Lista de cada servicio por separado, ej: "Servicio Aeropuerto 25/07", "Consulta Viaje a Toledo"]

---

**PARA CADA SERVICIO:**

**ESTADO ACTUAL:**
[Confirmado / Pendiente de Confirmación del Cliente / Pendiente de Cotización / Pagado / Cancelado]

**INFORMACIÓN DEL SERVICIO:**
- **Servicio:** [Descripción breve, ej: Recogida en Aeropuerto T4]
- **Fecha y Hora:** [Fecha y hora exactas]
- **Recogida:** [Dirección o lugar de recogida]
- **Destino:** [Dirección o lugar de destino]
- **Precio:** [Monto acordado]
- **Estado de Pago:** [Pagado (Bizum/Transf.) / Pendiente de Pago en Efectivo / Pendiente de Pago]

**TAREAS PENDIENTES PARA MÍ:**
[Qué es lo siguiente que tengo que hacer yo para este servicio específico. Ej: "Enviar confirmación final", "Cotizar el precio", "Preguntar número de pasajeros", "Nada, solo realizar el servicio."]

---
**Instrucción crítica:** Antes de responder, analiza toda la conversación y separa cada viaje o solicitud en su propia sección, tal como se indica en el formato de respuesta.

---

### Tu Flujo de Trabajo para Gestionar WhatsApp

1.  **Selecciona una conversación importante** en WhatsApp.
2.  Usa la función de **"Exportar chat"** (sin archivos) para copiar todo el texto.
3.  Abre un chat con la IA (ChatGPT, Gemini, etc.).
4.  **Pega PRIMERO tu prompt adaptado** que te he preparado arriba.
5.  **Pega DEBAJO el texto de la conversación** que has exportado de WhatsApp.
6.  **Envía el mensaje.**

La IA te devolverá un resumen perfectamente estructurado con todo lo que tienes confirmado y pendiente con ese cliente, listo para que lo uses para actualizar tu Trello, Airtable o simplemente para tener claridad mental. **Es como tener un secretario personal que ordena tus conversaciones.**