import json
from pathlib import Path
import datetime as _dt

# ---------- Flags locales (festivo / nocturno) ----------
_CACHE_PATH = Path("runtime") / "cached_context.json"
_FESTIVOS_SET = set()
try:
    if _CACHE_PATH.exists():
        _cache = json.loads(_CACHE_PATH.read_text(encoding="utf-8"))
        for f in _cache.get("data", {}).get("festivos", []):
            if f.get("fecha"):
                _FESTIVOS_SET.add(f["fecha"])
except Exception:
    pass

def is_night(hhmm: str | None) -> bool:
    if not hhmm or len(hhmm) < 4 or ":" not in hhmm:
        return False
    try:
        h, m = hhmm.split(":")
        h = int(h)
        # [22:00–23:59] ∪ [00:00–05:59]
        return h >= 22 or h < 6
    except:
        return False

def ensure_flags(svc: dict) -> dict:
    flags = svc.get("flags") or {}
    # is_night
    if flags.get("is_night") is None:
        flags["is_night"] = is_night(svc.get("time"))
    # is_festive (si viene fecha normalizada)
    if flags.get("is_festive") is None:
        date = svc.get("date")
        flags["is_festive"] = bool(date and date in _FESTIVOS_SET)
    svc["flags"] = flags
    return svc
