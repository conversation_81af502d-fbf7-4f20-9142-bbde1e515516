# tests/test_itinerary.py
# Pytest de alto nivel sin llamadas reales a la API.
# Finge la salida del extractor y “monkeypatch” el motor de precios.

import pytest
import json

# --- Fixtures ---------------------------------------------------------------

@pytest.fixture
def mock_engine(monkeypatch):
    class DummyEngine:
        def calculate_price(self, svc):
            """Devuelve números realistas por tipo para testear sumas/flags."""
            t = (svc.get("type") or "").lower()
            from_ = (svc.get("from") or "").lower()
            to_   = (svc.get("to") or "").lower()
            pax   = svc.get("pax") or 1

            if t == "transfer":
                # Precios base ficticios:
                if "cdg" in from_ and "paris" in to_:
                    base = 120
                elif "paris" in from_ and "cdg" in to_:
                    base = 120
                elif "ory" in from_:
                    base = 95
                else:
                    base = 100

                # Nocturno
                time = svc.get("time")
                if time and (time >= "22:00" or time < "06:00"):
                    base = 140 if "cdg" in from_ else 115

                # Grupo >7: 2 vans + 10% coordinación (aprox)
                if pax and pax > 7:
                    base = round((base * 2) * 1.10)

                return {
                    "precio_final_num": float(base),
                    "precio_final_calculado": f"{base}€",
                    "detalles": {"base": base}
                }

            if t == "hourly":
                dur = svc.get("duration_hours") or 3
                base = 85 * float(dur)
                return {
                    "precio_final_num": float(base),
                    "precio_final_calculado": f"{base}€",
                    "detalles": {"hora": 85, "dur": dur}
                }

            # Default genérico
            return {
                "precio_final_num": 100.0,
                "precio_final_calculado": "100€",
                "detalles": {}
            }

    # monkeypatch
    import sys
    sys.modules['runtime.quoting_engine'] = type(sys)("runtime.quoting_engine")
    sys.modules['runtime.quoting_engine'].QuotingEngine = DummyEngine
    return DummyEngine()

# --- Helpers ----------------------------------------------------------------

def sum_total_from_quotes(quotes):
    total = 0.0
    for q in quotes:
        if q.get("precio_final_num") is not None:
            total += float(q["precio_final_num"])
        else:
            raw = str(q.get("precio_final_calculado", "0")).replace("€"," ").replace(",",".").strip()
            try:
                total += float(raw)
            except:
                pass
    return round(total, 2)

# --- Tests ------------------------------------------------------------------

def test_paquete_descuento_tres_servicios(mock_engine):
    """3 servicios → aplicar -5% sobre la suma."""
    services = [
        {"type":"transfer","from":"CDG","to":"Paris intra-muros","date":"2025-08-15","time":"10:00","pax":4},
        {"type":"hourly","from":"Paris intra-muros","date":"2025-08-16","duration_hours":4,"pax":4},
        {"type":"transfer","from":"Paris intra-muros","to":"CDG","date":"2025-08-17","time":"07:00","pax":4}
    ]

    engine = mock_engine
    quotes = [engine.calculate_price(svc) for svc in services]
    total = sum_total_from_quotes(quotes)
    # base: 120 + (85*4=340) + 120 = 580
    assert total == 580.0

    # aplica -5%
    total_desc = round(total * 0.95, 2)
    assert total_desc == 551.0

def test_missing_info_obliga_aclaracion():
    """Si extract devuelve missing_info, el flujo debe pedir aclaración."""
    extracted = {
        "services": [
            {"type":"hourly","date":"2025-08-16","time":None,"from":"Paris intra-muros","duration_hours":4,"pax":4}
        ],
        "missing_info": ["16/08 - hora exacta"],
        "language": "ES"
    }
    assert extracted["missing_info"], "Debe existir al menos un dato faltante"

def test_fallback_simple_detecta_aeropuerto(mock_engine):
    """Si el extractor falla, el fallback mínimo detecta CDG/ORY y construye UN transfer."""
    text = "hola necesito traslado desde cdg hoy"
    # Simular fallback del propio app
    svc = {
        "type":"transfer","date":None,"time":None,"from":"CDG","to":"Paris intra-muros",
        "duration_hours":None,"pax":None,
        "luggage":{"hold":None,"extra":None},
        "child_seats":0,"notes":None,
        "flags":{"is_festive":False,"is_night":False,"urgency_hours":None}
    }

    q = mock_engine.calculate_price(svc)
    assert q["precio_final_num"] in (120.0, 100.0)  # según heurística
