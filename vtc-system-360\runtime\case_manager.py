#!/usr/bin/env python3
"""
Case Manager - Sistema de seguimiento persistente de casos VTC 360°
Mantiene el estado de cada caso entre sesiones de VSCode
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional

class CaseManager:
    def __init__(self, tracking_file: str = "runtime/case_tracking.json"):
        self.tracking_file = tracking_file
        self.data = self.load_tracking_data()
    
    def load_tracking_data(self) -> Dict:
        """Cargar datos de seguimiento desde JSON"""
        if os.path.exists(self.tracking_file):
            with open(self.tracking_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"cases": {}, "active_case": None, "last_session": None}
    
    def save_tracking_data(self):
        """Guardar datos de seguimiento a JSON"""
        self.data["last_session"] = datetime.now().isoformat()
        with open(self.tracking_file, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False)
    
    def create_case(self, client_name: str, dates: str, services_count: int = 0, 
                   estimated_value: float = 0, notes: str = "") -> str:
        """Crear nuevo caso"""
        case_id = f"{client_name.lower().replace(' ', '_')}_{dates.replace('-', '_')}"
        
        self.data["cases"][case_id] = {
            "client_name": client_name,
            "dates": dates,
            "case_id": case_id,
            "hubspot_deal_id": None,
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "last_updated": datetime.now().strftime("%Y-%m-%d"),
            "current_phase": "STEP_1_CREATE_DEAL",
            "steps_completed": {
                "step_1_create_deal": False,
                "step_2_extract_itinerary": False,
                "step_3_send_quote": False,
                "step_4_client_confirmed": False,
                "step_5_update_deal_confirmed": False,
                "step_6_payment_received": False,
                "step_7_generate_airtable": False,
                "step_8_dispatch_driver": False
            },
            "services_count": services_count,
            "estimated_value": estimated_value,
            "notes": notes,
            "next_action": "Crear deal en HubSpot usando crear-deal-hubspot.md",
            "files_generated": [],
            "payment_status": "pending",
            "urgency": "normal"
        }
        
        self.data["active_case"] = case_id
        self.save_tracking_data()
        return case_id
    
    def get_case_status(self, case_id: Optional[str] = None) -> Dict:
        """Obtener estado actual de un caso"""
        if case_id is None:
            case_id = self.data.get("active_case")
        
        if not case_id or case_id not in self.data["cases"]:
            return {"error": "Caso no encontrado"}
        
        case = self.data["cases"][case_id]
        completed_steps = sum(1 for step in case["steps_completed"].values() if step)
        total_steps = len(case["steps_completed"])
        progress = (completed_steps / total_steps) * 100
        
        return {
            "case": case,
            "progress": {
                "completed": completed_steps,
                "total": total_steps,
                "percentage": progress
            },
            "next_step": self.get_next_step(case)
        }
    
    def get_next_step(self, case: Dict) -> Dict:
        """Determinar el siguiente paso a realizar"""
        steps = case["steps_completed"]
        step_info = {
            "step_1_create_deal": {
                "name": "Crear Deal HubSpot",
                "prompt": "crear-deal-hubspot.md",
                "description": "Crear deal en HubSpot con datos del cliente"
            },
            "step_2_extract_itinerary": {
                "name": "Procesar Itinerario", 
                "prompt": "extract-itinerary.md",
                "description": "Extraer servicios del itinerario y calcular precios"
            },
            "step_3_send_quote": {
                "name": "Enviar Cotización",
                "prompt": "mensaje-cotizacion-final.md", 
                "description": "Generar y enviar email de cotización al cliente"
            },
            "step_4_client_confirmed": {
                "name": "Confirmación Cliente",
                "prompt": "check-case-status.md",
                "description": "Verificar si el cliente confirmó los servicios"
            },
            "step_5_update_deal_confirmed": {
                "name": "Actualizar Deal Confirmado",
                "prompt": "actualizar-deal-hubspot.md",
                "description": "Mover deal a 'Confirmé (En attente de paiement)'"
            },
            "step_6_payment_received": {
                "name": "Confirmar Pago",
                "prompt": "actualizar-deal-hubspot.md",
                "description": "Confirmar pago recibido y mover deal a 'Payé'"
            },
            "step_7_generate_airtable": {
                "name": "Generar CSV Operativo",
                "prompt": "generate-airtable-csv.md",
                "description": "Convertir servicios a formato Airtable"
            },
            "step_8_dispatch_driver": {
                "name": "Dispatch Conductor",
                "prompt": "dispatch-conductor.md", 
                "description": "Generar órdenes de trabajo para conductor"
            }
        }
        
        for step_key, completed in steps.items():
            if not completed:
                return step_info[step_key]
        
        return {"name": "Caso Completado", "prompt": None, "description": "Todos los pasos completados"}
    
    def mark_step_completed(self, case_id: str, step: str, notes: str = ""):
        """Marcar un paso como completado"""
        if case_id in self.data["cases"]:
            self.data["cases"][case_id]["steps_completed"][step] = True
            self.data["cases"][case_id]["last_updated"] = datetime.now().strftime("%Y-%m-%d")
            if notes:
                self.data["cases"][case_id]["notes"] += f"\n{datetime.now().strftime('%Y-%m-%d')}: {notes}"
            self.save_tracking_data()
    
    def set_active_case(self, case_id: str):
        """Establecer caso activo"""
        if case_id in self.data["cases"]:
            self.data["active_case"] = case_id
            self.save_tracking_data()
    
    def list_cases(self) -> List[Dict]:
        """Listar todos los casos"""
        return [
            {
                "case_id": case_id,
                "client_name": case["client_name"],
                "dates": case["dates"],
                "progress": sum(1 for step in case["steps_completed"].values() if step),
                "total_steps": len(case["steps_completed"]),
                "status": case["payment_status"]
            }
            for case_id, case in self.data["cases"].items()
        ]

def main():
    """Función principal para uso desde línea de comandos"""
    import sys
    
    manager = CaseManager()
    
    if len(sys.argv) < 2:
        print("Uso: python case_manager.py [comando] [argumentos]")
        print("Comandos: status, create, complete, list")
        return
    
    command = sys.argv[1]
    
    if command == "status":
        case_id = sys.argv[2] if len(sys.argv) > 2 else None
        status = manager.get_case_status(case_id)
        print(json.dumps(status, indent=2, ensure_ascii=False))
    
    elif command == "create":
        if len(sys.argv) < 4:
            print("Uso: python case_manager.py create [nombre_cliente] [fechas]")
            return
        client_name = sys.argv[2]
        dates = sys.argv[3]
        case_id = manager.create_case(client_name, dates)
        print(f"Caso creado: {case_id}")
    
    elif command == "complete":
        if len(sys.argv) < 4:
            print("Uso: python case_manager.py complete [case_id] [step]")
            return
        case_id = sys.argv[2]
        step = sys.argv[3]
        notes = sys.argv[4] if len(sys.argv) > 4 else ""
        manager.mark_step_completed(case_id, step, notes)
        print(f"Paso {step} marcado como completado")
    
    elif command == "list":
        cases = manager.list_cases()
        for case in cases:
            print(f"{case['case_id']}: {case['client_name']} ({case['progress']}/{case['total_steps']})")

if __name__ == "__main__":
    main()
