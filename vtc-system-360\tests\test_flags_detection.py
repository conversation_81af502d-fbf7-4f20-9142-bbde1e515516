# tests/test_flags_detection.py
import pytest
import json
from pathlib import Path

# Importar las funciones directamente desde runtime.utils
from runtime.utils import is_night, ensure_flags, _FESTIVOS_SET, _CACHE_PATH

# Recargar app para asegurar que los cambios en _CACHE_PATH etc. se apliquen
@pytest.fixture(autouse=True)
def reload_utils_cache():
    # Forzar la recarga del set de festivos en utils.py para cada test
    _FESTIVOS_SET.clear()
    try:
        if _CACHE_PATH.exists():
            _cache = json.loads(_CACHE_PATH.read_text(encoding="utf-8"))
            for f in _cache.get("data", {}).get("festivos", []):
                if f.get("fecha"):
                    _FESTIVOS_SET.add(f["fecha"])
    except Exception:
        pass

# --- Tests para is_night --- (función local en app.py)

def test_is_night_true_late_night():
    assert is_night("23:30") == True

def test_is_night_true_early_morning():
    assert is_night("05:00") == True

def test_is_night_false_day():
    assert is_night("14:00") == False

def test_is_night_false_boundary_start():
    assert is_night("06:00") == False

def test_is_night_false_boundary_end():
    assert is_night("21:59") == False

def test_is_night_invalid_input():
    assert is_night(None) == False
    assert is_night("abc") == False

# --- Tests para ensure_flags --- (función local en app.py)

@pytest.fixture
def mock_festivos_cache(monkeypatch):
    # Simular un caché con festivos conocidos
    mock_cache_content = {
        "data": {
            "festivos": [
                {"fecha": "2025-01-01", "nombre": "Año Nuevo"},
                {"fecha": "2025-07-14", "nombre": "Día de la Bastilla"}
            ]
        }
    }
    # Asegurarse de que _CACHE_PATH exista y contenga el mock
    mock_cache_path = Path("runtime") / "cached_context.json"
    mock_cache_path.parent.mkdir(exist_ok=True)
    mock_cache_path.write_text(json.dumps(mock_cache_content))

    # Forzar a _FESTIVOS_SET a recargarse con el mock
    _FESTIVOS_SET.clear()
    for f in mock_cache_content.get("data", {}).get("festivos", []):
        if f.get("fecha"):
            _FESTIVOS_SET.add(f["fecha"])

    yield # Ejecutar el test

    # Limpiar el mock después del test
    mock_cache_path.unlink()

def test_ensure_flags_detects_festive(mock_festivos_cache):
    svc = {"date": "2025-07-14", "time": "10:00", "flags": {}}
    updated_svc = ensure_flags(svc)
    assert updated_svc["flags"]["is_festive"] == True
    assert updated_svc["flags"]["is_night"] == False

def test_ensure_flags_detects_night():
    svc = {"date": "2025-01-02", "time": "01:30", "flags": {}}
    updated_svc = ensure_flags(svc)
    assert updated_svc["flags"]["is_night"] == True
    assert updated_svc["flags"]["is_festive"] == False

def test_ensure_flags_already_set():
    svc = {"date": "2025-01-01", "time": "12:00", "flags": {"is_festive": False, "is_night": True}}
    updated_svc = ensure_flags(svc)
    # Los flags existentes no deben ser sobrescritos si ya están presentes
    assert updated_svc["flags"]["is_festive"] == False
    assert updated_svc["flags"]["is_night"] == True

def test_ensure_flags_no_date_or_time():
    svc = {"flags": {}}
    updated_svc = ensure_flags(svc)
    assert updated_svc["flags"]["is_festive"] == False
    assert updated_svc["flags"]["is_night"] == False