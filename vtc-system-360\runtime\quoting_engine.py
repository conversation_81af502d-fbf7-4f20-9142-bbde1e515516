import json
from pathlib import Path
from datetime import datetime

class QuotingEngine:
    def __init__(self, cache_path=None):
        if cache_path is None:
            base_dir = Path(__file__).parent.parent
            final_path = base_dir / "runtime/cached_context.json" # Corrected path
        else:
            final_path = Path(cache_path)

        if not final_path.exists():
            raise FileNotFoundError(f"El archivo de caché no se encuentra en {final_path}. Ejecuta init_cache.py --build")
            
        with open(final_path, 'r', encoding='utf-8') as f:
            self.cache = json.load(f)
        
        self.reglas = self.cache.get('data', {}).get('reglas', {})
        self.tarifas = self.cache.get('data', {}).get('tarifas', {})
        self.festivos = [item['fecha'] for item in self.cache.get('data', {}).get('festivos', [])]

    def _normalize_service_type(self, tipo_raw):
        """Normaliza diferentes términos de servicios a términos estándar.

        Soporta términos en múltiples idiomas y variaciones comunes
        utilizadas por agencias de turismo internacionales.
        """
        if not tipo_raw:
            return None

        tipo_lower = tipo_raw.lower().strip()

        # TRASLADOS / TRANSFERS
        traslados = [
            # Español
            'traslado', 'traslados', 'transfer', 'transporte', 'transportes',
            'pickup', 'drop-off', 'ida', 'vuelta', 'regreso',
            # Francés
            'transfert', 'transferts', 'transport', 'transports', 'navette',
            'navettes', 'aller', 'retour', 'aller-retour',
            # Inglés
            'transfer', 'transfers', 'transportation', 'shuttle', 'ride',
            'pickup', 'dropoff', 'one-way', 'round-trip',
            # Italiano
            'trasferimento', 'trasferimenti', 'trasporto', 'navetta',
            # Alemán
            'transfer', 'transport', 'fahrt', 'shuttle',
            # Portugués
            'transferencia', 'transporte', 'translado'
        ]

        # DISPOSICIÓN / HOURLY
        disposicion = [
            # Español
            'hourly', 'disposicion', 'disposición', 'horas', 'por horas',
            'disponibilidad', 'servicio por horas', 'tiempo completo',
            # Francés
            'disposition', 'à disposition', 'horaire', 'par heure',
            'service horaire', 'temps complet', 'journée complète',
            # Inglés
            'hourly', 'by the hour', 'hourly service', 'at disposal',
            'full day', 'half day', 'daily', 'time-based',
            # Italiano
            'disposizione', 'orario', 'giornata completa',
            # Alemán
            'stündlich', 'zur verfügung', 'ganztags',
            # Portugués
            'disposição', 'por hora', 'dia completo'
        ]

        # TOURS / EXCURSIONES
        tours = [
            # Español
            'tour', 'tours', 'excursion', 'excursión', 'excursiones',
            'visita', 'visitas', 'recorrido', 'recorridos', 'paseo',
            'city tour', 'sightseeing',
            # Francés
            'tour', 'tours', 'excursion', 'excursions', 'visite',
            'visites', 'circuit', 'circuits', 'promenade',
            # Inglés
            'tour', 'tours', 'excursion', 'excursions', 'sightseeing',
            'city tour', 'guided tour', 'visit', 'visits',
            # Italiano
            'tour', 'escursione', 'escursioni', 'visita', 'visite',
            # Alemán
            'tour', 'ausflug', 'ausflüge', 'besichtigung', 'rundfahrt',
            # Portugués
            'tour', 'excursão', 'excursões', 'visita', 'passeio'
        ]

        # GUÍAS
        guias = [
            # Español
            'guia', 'guía', 'guias', 'guías', 'guide', 'acompañante',
            'interprete', 'intérprete',
            # Francés
            'guide', 'guides', 'accompagnateur', 'interprète',
            # Inglés
            'guide', 'guides', 'tour guide', 'interpreter', 'escort',
            # Italiano
            'guida', 'guide', 'accompagnatore', 'interprete',
            # Alemán
            'führer', 'guide', 'reiseführer', 'dolmetscher',
            # Portugués
            'guia', 'acompanhante', 'intérprete'
        ]

        # SERVICIOS ESPECIALES
        especiales = [
            # Eventos
            'evento', 'event', 'événement', 'wedding', 'boda', 'mariage',
            'conference', 'conferencia', 'conférence', 'meeting',
            # Aeropuerto específico
            'airport', 'aeropuerto', 'aéroport', 'aeroporto', 'flughafen',
            # Espera
            'waiting', 'espera', 'attente', 'standby', 'disponible'
        ]

        # Normalización
        if tipo_lower in traslados:
            return 'traslado'
        elif tipo_lower in disposicion:
            return 'hourly'
        elif tipo_lower in tours:
            return 'tour'
        elif tipo_lower in guias:
            return 'guia'
        elif tipo_lower in especiales:
            # Para servicios especiales, intentar inferir el tipo más apropiado
            if any(term in tipo_lower for term in ['airport', 'aeropuerto', 'aéroport']):
                return 'traslado'
            elif any(term in tipo_lower for term in ['waiting', 'espera', 'attente', 'disponible']):
                return 'hourly'
            else:
                return 'evento'  # Nuevo tipo para eventos especiales
        else:
            return tipo_lower  # Devolver tal como está si no se reconoce

    def _es_nocturno(self, hora_str):
        if not hora_str or not self.reglas.get('horario_nocturno'):
            return False
        
        hora_servicio = datetime.strptime(hora_str, '%H:%M').time()
        inicio_nocturno = datetime.strptime(self.reglas['horario_nocturno']['inicio'], '%H:%M').time()
        fin_nocturno = datetime.strptime(self.reglas['horario_nocturno']['fin'], '%H:%M').time()

        if inicio_nocturno > fin_nocturno: # El horario cruza la medianoche (e.g., 22:00 - 06:00)
            return hora_servicio >= inicio_nocturno or hora_servicio < fin_nocturno
        else: # El horario es en el mismo día
            return inicio_nocturno <= hora_servicio < fin_nocturno

    def _es_festivo(self, fecha_str):
        if not fecha_str or not self.festivos:
            return False
        return fecha_str in self.festivos

    def calculate_price(self, request_json):
        """
        Calcula el precio de un servicio basado en una solicitud JSON estructurada.
        Adapta la entrada del extract.json.
        """
        # Ahora request_json es directamente el diccionario de servicio (svc)
        servicio = request_json
        
        tipo_servicio_raw = servicio.get('type') # Usar 'type' de extract.json
        # Normalizar términos para flexibilidad
        tipo_servicio = self._normalize_service_type(tipo_servicio_raw)
        origen = servicio.get('from') # Usar 'from' de extract.json
        hora = servicio.get('time') # Usar 'time' de extract.json
        fecha = servicio.get('date') # Usar 'date' de extract.json
        duration_hours = servicio.get('duration_hours', 0) # Para servicios por hora
        
        precio_base = 0
        detalles = []

        if tipo_servicio == 'traslado':
            # Mapear ubicaciones a claves de caché
            key_aeropuerto = None
            if origen and 'cdg' in origen.lower():
                key_aeropuerto = 'cdg'
            elif origen and 'ory' in origen.lower():
                key_aeropuerto = 'ory'
            
            if key_aeropuerto:
                if self._es_nocturno(hora):
                    precio_base = self.tarifas.get(key_aeropuerto, {}).get('noche', 0)
                    detalles.append("Tarifa nocturna aplicada")
                else:
                    precio_base = self.tarifas.get(key_aeropuerto, {}).get('dia', 0)
                    detalles.append("Tarifa diurna aplicada")
            else:
                # Si no es aeropuerto, usar tarifa de disposición por defecto para traslados
                precio_base = self.tarifas.get('disposicion_hora', 85) # Asumimos 1 hora si no es aeropuerto
                detalles.append("Tarifa base por traslado (no aeropuerto)")

        elif tipo_servicio == 'hourly':
            # Usar duración de horas para calcular el precio
            tarifa_hora = self.tarifas.get('disposicion_hora', 85)
            precio_base = tarifa_hora * duration_hours
            detalles.append(f"Tarifa por disposición ({duration_hours}h)")
            if self._es_nocturno(hora):
                recargo_nocturno = self.reglas.get('horario_nocturno', {}).get('recargo_porcentaje', 0)
                precio_base *= (1 + recargo_nocturno / 100)
                detalles.append(f"Recargo nocturno ({recargo_nocturno}%) aplicado")

        elif tipo_servicio == 'tour':
            # Tours/excursiones se calculan como disposición por horas
            tarifa_hora = self.tarifas.get('disposicion_hora', 85)
            precio_base = tarifa_hora * duration_hours
            detalles.append(f"Tarifa por tour/excursión ({duration_hours}h)")
            if self._es_nocturno(hora):
                recargo_nocturno = self.reglas.get('horario_nocturno', {}).get('recargo_porcentaje', 0)
                precio_base *= (1 + recargo_nocturno / 100)
                detalles.append(f"Recargo nocturno ({recargo_nocturno}%) aplicado")

        elif tipo_servicio == 'guia':
            # Tarifa de guía (ejemplo, no está en tarifas.md)
            # Asumimos una tarifa fija por hora para el guía
            tarifa_guia_hora = 50 # Ejemplo: 50€/hora
            precio_base = tarifa_guia_hora * duration_hours
            detalles.append(f"Tarifa por servicio de guía ({duration_hours}h)")

        elif tipo_servicio == 'evento':
            # Servicios especiales para eventos
            tarifa_hora = self.tarifas.get('disposicion_hora', 85)
            precio_base = tarifa_hora * duration_hours
            detalles.append(f"Tarifa por servicio especial/evento ({duration_hours}h)")

        else:
            return {"error": f"Tipo de servicio '{tipo_servicio}' no reconocido o no implementado."}


        precio_final = precio_base

        # Aplicar recargos (festivo)
        if self._es_festivo(fecha):
            recargo_festivo = self.reglas.get('recargo_festivo_porcentaje', 15) # Asumimos 15% si no está en reglas
            aumento = precio_final * (recargo_festivo / 100)
            precio_final += aumento
            detalles.append(f"Recargo festivo ({recargo_festivo}%) aplicado: +{aumento:.2f}€")

        # TODO: Implementar recargos por urgencia, pax, etc.
        # TODO: Implementar descuentos

        return {
            "precio_base": f"{precio_base:.2f}€",
            "precio_final_calculado": f"{precio_final:.2f}€",
            "precio_final_num": round(precio_final, 2), # Añadir versión numérica
            "detalles_calculo": detalles
        }

if __name__ == '__main__':
    # Ejemplo de uso para probar el motor de cálculo
    engine = QuotingEngine()

    # 1. Simular la salida del extractor para un traslado de día en festivo
    # Adaptado al nuevo formato de entrada
    solicitud_dia_festivo = {
        "type": "traslado",
        "date": "2025-08-15", # Festivo (Asunción)
        "time": "15:00",
        "from": "ORY", # Origen directo
        "to": "Paris",
        "pax": 2
    }
    
    # 2. Simular la salida para un traslado de noche
    solicitud_noche = {
        "type": "traslado",
        "date": "2025-08-16",
        "time": "23:00",
        "from": "CDG", # Origen directo
        "to": "Paris",
        "pax": 2
    }

    # 3. Simular un servicio por hora
    solicitud_hourly = {
        "type": "hourly",
        "date": "2025-08-11",
        "time": "10:00",
        "from": "Hotel",
        "to": "Paris",
        "duration_hours": 5,
        "pax": 1
    }

    # 4. Simular un servicio de guía
    solicitud_guia = {
        "type": "guia",
        "date": "2025-08-12",
        "time": "09:00",
        "from": "Hotel",
        "to": "Paris",
        "duration_hours": 4,
        "pax": 1
    }


    print("--- Calculando Cotización (Día / Festivo) ---")
    cotizacion1 = engine.calculate_price(solicitud_dia_festivo)
    print(json.dumps(cotizacion1, indent=2, ensure_ascii=False))

    print("\n--- Calculando Cotización (Noche) ---")
    cotizacion2 = engine.calculate_price(solicitud_noche)
    print(json.dumps(cotizacion2, indent=2, ensure_ascii=False))

    print("\n--- Calculando Cotización (Servicio por Hora) ---")
    cotizacion3 = engine.calculate_price(solicitud_hourly)
    print(json.dumps(cotizacion3, indent=2, ensure_ascii=False))

    print("\n--- Calculando Cotización (Servicio de Guía) ---")
    cotizacion4 = engine.calculate_price(solicitud_guia)
    print(json.dumps(cotizacion4, indent=2, ensure_ascii=False))