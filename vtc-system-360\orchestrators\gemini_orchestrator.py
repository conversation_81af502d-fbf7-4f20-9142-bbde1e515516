import json
import os
import re
import time
import random
from pathlib import Path
import google.generativeai as genai

# --- Carga de la API Key de forma segura ---
# Asumimos que load_dotenv() ya se llamó en app.py
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# --- Configuración del cliente Gemini ---
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
    except Exception as e:
        print(f"ERROR: La API key de Gemini parece inválida o ha ocurrido un error al configurar la librería: {e}")
        genai = None # Deshabilitar si falla la configuración

def call_gemini_with_retry(fn, max_attempts=3, base_wait=0.5):
    last_err = None
    for i in range(max_attempts):
        try:
            return fn()
        except Exception as e:
            last_err = e
            wait = base_wait * (2 ** i) + random.random() * 0.2
            print(f"Retry {i+1}/{max_attempts} en {wait:.1f}s… ({e})")
            time.sleep(wait)
    raise last_err

def get_suitable_gemini_model():
    """Busca un modelo Gemini que soporte generateContent."""
    if not genai:
        return None
    try:
        for m in genai.list_models():
            if "generateContent" in m.supported_generation_methods:
                return m.name
        print("ERROR: No se encontró ningún modelo Gemini compatible con 'generateContent'.")
        return None
    except Exception as e:
        print(f"ERROR al listar modelos de Gemini: {e}")
        return None

def extract_itinerary(user_text: str):
    """
    Llama a la API de Gemini para extraer detalles del itinerario.
    Devuelve: 
    - services: list[dict]
    - missing_info: list[str]
    - language: str|None
    """
    model_name = get_suitable_gemini_model()
    if not model_name:
        print("ERROR: No hay modelo Gemini disponible.")
        return {"services": [], "missing_info": ["No Gemini model available"], "language": None}

    print(f"\n--- 1. Llamando a la API de Gemini ({model_name}) → extract-itinerary ---")
    
    try:
        prompt_path = Path(__file__).parent.parent / "02-prompts/extract-itinerary.md"
        with open(prompt_path, 'r', encoding='utf-8') as f:
            prompt_template = f.read()

        messages = [
            {"role": "system", "content": prompt_template},
            {"role": "user", "content": f"{user_text}"}
        ]

        resp = call_gemini_with_retry(lambda: genai.GenerativeModel(model_name).generate_content(messages))

        payload = resp.text.strip()
        data = json.loads(payload)

        # Normalizar formatos: permitir lista directa o dict con services
        if isinstance(data, list):
            services = data
            missing = []
            lang = None
        else:
            services = data.get("services", [])
            missing = data.get("missing_info", [])
            lang = data.get("language")

        return {"services": services, "missing_info": missing, "language": lang}

    except Exception as e:
        print(f"ERROR Gemini extract-itinerary: {e}")
        return {"services": [], "missing_info": [str(e)], "language": None}
