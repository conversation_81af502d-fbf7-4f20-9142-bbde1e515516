# Sistema VTC 360° - Guía Completa Integrada

## 🏗️ ARQUITECTURA DEL SISTEMA

```
SISTEMA VTC 360° INTEGRADO
├── 🎩 HUBSPOT - Gestión Comercial (Pipeline de ventas)
├── 📝 PROMPTS - Interfaz IA (15 prompts especializados)
├── 🐍 PYTHON - Motor Cálculo (12 scripts + tests)
├── 📊 AIRTABLE - Operación (Planning y dispatch)
└── 📱 WHATSAPP - Comunicación (Conductores y clientes)
```

## 📖 ANTES DE EMPEZAR - LEE LA FILOSOFÍA

**⭐ FUNDAMENTAL**: Antes de usar el sistema, lee el manual conceptual:
- **`docs/SISTEMA_OPERATIVO_EMPRENDEDOR.md`** - La filosofía completa del sistema
- Explica el "por qué" detrás de cada decisión
- Del caos al control: transformación empresarial
- Dos sombreros, dos herramientas: HubSpot + Airtable

## 🚀 INSTALACIÓN RÁPIDA

```bash
# 1. <PERSON>lon<PERSON> el proyecto
git clone <URL-del-repo>
cd vtc-system-360

# 2. Instala dependencias Python
pip install -r requirements.txt

# 3. Configuración opcional (.env)
DEEPSEEK_API_KEY=tu_api_key_aqui
GEMINI_API_KEY=tu_api_key_aqui
EXTRACTOR=gemini
PACKAGE_DISCOUNT=0.05
```

## 🤖 SECRETARIO INTELIGENTE IA - COMANDO MAESTRO

**⭐ SIEMPRE AL ABRIR VSCODE:**
```
@check-case-status.md
¿Dónde estoy?
```

**Resultado**: La IA lee tu estado actual y te guía paso a paso desde donde te quedaste, sin importar cuánto tiempo haya pasado.

## 🔄 FLUJO COMERCIAL COMPLETO

### FASE 1: REGISTRO COMERCIAL (HUBSPOT)
```
📧 Solicitud Cliente/Agencia
         ↓
@crear-deal-hubspot.md
         ↓
🎩 Deal creado en HubSpot
Pipeline: "Nouvelle Demande" (25%)
```

### FASE 2: COTIZACIÓN TÉCNICA
```
@extract-itinerary.md → JSON → Python → Cálculo
         ↓
@mensaje-cotizacion-final.md
         ↓
📧 Email cotización enviado
         ↓
@actualizar-deal-hubspot.md
Pipeline: "Cotisation Envoyée" (50%)
```

### FASE 3: CONFIRMACIÓN Y PAGO
```
✅ Cliente acepta → Pipeline: "Confirmé" (75%)
💳 Pago recibido → Pipeline: "Payé" (100%)
```

### FASE 4: OPERACIÓN
```
@generate-airtable-csv.md → 📊 Airtable
@dispatch-conductor.md → 📱 WhatsApp conductores
```

## ⚡ GUÍA RÁPIDA POR CASO DE USO

### COTIZACIÓN RÁPIDA (Boris)
```
@paris-elite-services-v7.md
[Pegar itinerario del cliente]
```
**Resultado**: Cotización lista para enviar (30 segundos)

### AGENCIA INTERNACIONAL (Stephanie)
```
1. @crear-deal-hubspot.md
2. @extract-itinerary.md → extract.json
3. python app.py --in-json extract.json
4. @mensaje-cotizacion-final.md
```
**Resultado**: Proceso comercial completo (3 minutos)

### EXPERIENCIA VIP
```
@asistente-vip-experiencias.md
[Consulta personalizada]
```
**Resultado**: Propuesta experiencial premium (10 minutos)

### EMAILS COMPLEJOS
```
@clarificacion-emails-cotizaciones.md
[Pegar hilo completo de emails]
```
**Resultado**: Separación clara de proyectos y estados

### DISPATCH OPERATIVO
```
@dispatch-conductor.md
[Servicios confirmados]
```
**Resultado**: Órdenes de trabajo sin información financiera

## 📋 COLECCIÓN COMPLETA DE PROMPTS (15)

### 🏢 PROMPTS ESPECÍFICOS DE TU NEGOCIO (7)
1. **`paris-elite-services-v7.md`** - Cotizaciones Boris
2. **`asistente-vip-experiencias.md`** - Planificador VIP  
3. **`sistema-operativo-empresarial.md`** - Workflow maestro
4. **`clarificacion-emails-cotizaciones.md`** - Análisis emails
5. **`crear-deal-hubspot.md`** - Registro comercial
6. **`mensaje-cotizacion-final.md`** - Email cotización
7. **`actualizar-deal-hubspot.md`** - Seguimiento pipeline

### 🌍 PROMPTS SISTEMA UNIVERSAL (8)
8. **`extract-itinerary.md`** - Extracción multilingüe
9. **`dispatch-conductor.md`** - Dispatch operativo
10. **`extract-request-details.md`** - Análisis detallado
11. **`generate-airtable-csv.md`** - Exportación datos
12. **`gemini-v2/extract-itinerary.md`** - Extracción optimizada
13. **`gemini-v2/calculate-quote.md`** - Cálculo directo
14. **`gemini-v2/generate-reply.md`** - Respuestas WhatsApp
15. **`vscode-prompts/extract-itinerary-gemini.md`** - Versión VSCode

## 🐍 SCRIPTS PYTHON (12)

### 🎯 SCRIPTS PRINCIPALES (3)
- **`app.py`** - Aplicación principal y CLI
- **`migration_validator.py`** - Validador de migración
- **`procesar_reserva.bat`** - Script batch

### ⚙️ MÓDULOS RUNTIME (6)
- **`runtime/quoting_engine.py`** - Motor de cotización
- **`runtime/session_manager.py`** - Gestor de sesiones
- **`runtime/init_cache.py`** - Inicializador de caché
- **`runtime/utils.py`** - Utilidades
- **`runtime/test_scenarios.py`** - Escenarios de prueba
- **`runtime/__init__.py`** - Inicializador del módulo

### 🧪 TESTS AUTOMATIZADOS (4)
- **`tests/test_env_discount.py`** - Test descuentos
- **`tests/test_flags_detection.py`** - Test detección flags
- **`tests/test_itinerary.py`** - Test itinerarios
- **`tests/conftest.py`** - Configuración tests

### 🔗 ORQUESTADORES (1)
- **`orchestrators/gemini_orchestrator.py`** - Orquestador Gemini

## 🎯 CASOS DE USO DETALLADOS

### CASO 1: NUEVA SOLICITUD DE AGENCIA
```bash
# 1. Registrar en HubSpot
@crear-deal-hubspot.md
"Stephanie de VIAMOSA solicita servicios Sara Waisburd"

# 2. Procesar itinerario
@extract-itinerary.md
[Pegar itinerario] → extract.json

# 3. Calcular precios
python app.py --in-json extract.json --out cotizacion.json

# 4. Generar email cotización
@mensaje-cotizacion-final.md
[Usar datos de cotizacion.json]

# 5. Actualizar HubSpot
@actualizar-deal-hubspot.md
Pipeline: "Cotisation Envoyée"
```

### CASO 2: CONFIRMACIÓN Y OPERACIÓN
```bash
# 1. Cliente confirma
@actualizar-deal-hubspot.md
Pipeline: "Confirmé (En attente de paiement)"

# 2. Pago recibido
@actualizar-deal-hubspot.md
Pipeline: "Payé (Service à réaliser)"

# 3. Generar CSVs para Airtable
@generate-airtable-csv.md
→ Reservaciones.csv, Clientes.csv, Servicios.csv

# 4. Crear dispatch conductores
@dispatch-conductor.md
→ Órdenes de trabajo para WhatsApp
```

## 🔧 COMANDOS ÚTILES

### Procesamiento Estándar
```bash
# Modo offline (recomendado)
python app.py --in-json extract.json --skip-llm --out cotizacion.json

# Con idioma específico
python app.py --in-json extract.json --lang fr --out cotizacion.json

# Formato Markdown
python app.py --in-json extract.json --out-format md --out cotizacion.md
```

### Tests y Validación
```bash
# Ejecutar todos los tests
pytest

# Test específico
pytest tests/test_flags_detection.py

# Validar migración
python migration_validator.py
```

## 📊 MÉTRICAS Y ANÁLISIS

### Archivos de Análisis
- **`04-analytics/daily/`** - Reportes diarios
- **`04-analytics/weekly/`** - Reportes semanales  
- **`04-analytics/monthly/`** - Reportes mensuales
- **`04-analytics/migration_report_*.txt`** - Reportes de migración

### Sesiones
- **`runtime/sessions/`** - Historial de sesiones
- **`runtime/sessions/current_session.json`** - Sesión actual

## 🚨 TROUBLESHOOTING

### Errores Comunes
- **ImportError**: `pip install -r requirements.txt`
- **API Key inválida**: Revisar archivo `.env`
- **JSON malformado**: Validar salida de Gemini
- **Sesión corrupta**: Eliminar `runtime/sessions/current_session.json`

### Logs y Debug
```bash
# Modo verbose
python app.py --in-json extract.json --verbose

# Debug completo
python app.py --in-json extract.json --debug
```

---

## 🎯 FILOSOFÍA DEL SISTEMA

**"Lo mejor de ambos mundos"**
- **IA para creatividad** (entender, extraer, generar)
- **Python para precisión** (calcular, validar, procesar)
- **Datos para flexibilidad** (configurar, escalar, auditar)

**Resultado**: Sistema híbrido que combina la inteligencia artificial con la precisión matemática y la flexibilidad operativa.
