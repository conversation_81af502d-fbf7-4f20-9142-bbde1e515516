import io
import sys
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

import json
import os
import argparse
from pathlib import Path
from datetime import datetime
from runtime.quoting_engine import QuotingEngine
from runtime.utils import ensure_flags as _ensure_flags

# --- Descuento de paquete parametrizable ---
PACKAGE_DISCOUNT = os.getenv("PACKAGE_DISCOUNT", "0.05")
try:
    PACKAGE_DISCOUNT = max(0.0, min(0.15, float(PACKAGE_DISCOUNT)))
except:
    PACKAGE_DISCOUNT = 0.05

# --- Funciones de utilidad ---

def extract_details_simple(user_text: str):
    """Mini extractor de emergencia para modo interactivo."""
    aer = "CDG" if "cdg" in user_text.lower() else ("ORY" if "orly" in user_text.lower() or "ory" in user_text.lower() else None)
    if not aer:
        return None
    return {
        "services": [{
            "type": "transfer",
            "date": None,
            "time": None,
            "from": aer,
            "to": "Paris intra-muros",
            "duration_hours": None,
            "pax": None,
            "luggage": {"hold": None, "extra": None},
            "child_seats": 0,
            "notes": None,
            "flags": {"is_festive": False, "is_night": False, "urgency_hours": None}
        }],
        "missing_info": ["fecha", "hora", "pax"],
        "language": None
    }

def coerce_types(svc: dict) -> dict:
    """Asegura que los tipos de datos son correctos."""
    def to_int(x):
        try: return int(x)
        except: return None
    def to_float(x):
        try: return float(x)
        except: return None

    svc["pax"] = to_int(svc.get("pax"))
    if svc.get("duration_hours") is not None:
        svc["duration_hours"] = to_float(svc.get("duration_hours"))
    lug = svc.get("luggage") or {}
    if isinstance(lug.get("hold"), str):
        lug["hold"] = to_int(lug["hold"])
    svc["luggage"] = lug
    return svc

def build_whatsapp_summary_like(services, total, lang="es"):
    """Crea el resumen para WhatsApp."""
    es = (lang or "es").upper().startswith("ES")
    lines = ["✅ Itinerario confirmado:" if es else "✅ Itinéraire confirmé:"]
    for svc in services:
        t = (svc.get("type") or " ").lower()
        date = svc.get("date"," ")
        time = svc.get("time") or "—"
        pax  = svc.get("pax") or "—"

        if t == "transfer":
            from_txt = svc.get("from") or "?"
            to_txt   = svc.get("to") or "?"
            s = f"• {date} {time} {from_txt}→{to_txt} ({pax} pax)"
        elif t == "hourly":
            dur = svc.get("duration_hours") or "?"
            s = f"• {date} {time} Disposición {dur}h ({pax} pax)"
        else:
            s = f"• {date} {time} {t} ({pax} pax)"
        lines.append(s)

    lines.append(f"Total estimado: {round(float(total or 0))}€")
    lines.append("¿Confirmo y genero el link de pago? 📲" if es
                 else "Je confirme et j’envoie le lien de paiement ? 📲")
    return "\n".join(lines)

def _process_text(texto_solicitud: str, preferred_lang: str | None = None, args=None, in_json_data: dict | None = None):
    services = []
    missing = []
    language = (preferred_lang or "es").upper()

    if in_json_data:
        services = in_json_data.get("services", []) or []
        missing = in_json_data.get("missing_info", []) or []
        language = (preferred_lang or in_json_data.get("language") or "es").upper()
    else:
        print("ADVERTENCIA: No se proporcionó un archivo JSON. Usando el extractor local simple.")
        extracted = extract_details_simple(texto_solicitud)
        if not extracted:
            print("❌ No se pudo extraer información con el extractor simple.")
            return
        services = extracted.get("services", []) or []
        missing  = extracted.get("missing_info", []) or []
        language = (preferred_lang or extracted.get("language") or "es").upper()

    if not services:
        print("❌ No hay servicios para procesar.")
        return

    if missing:
        print("\n--- 2. Datos faltantes para cerrar precio ---")
        for i, m in enumerate(missing, 1):
            print(f"{i}. {m}")
        print("\n👉 Por favor, completa la información en el JSON de entrada y vuelve a ejecutar.")
        return

    engine = QuotingEngine()
    total = 0.0
    print("\n--- 3. Calculando precios por servicio ---")
    for i, raw_svc in enumerate(services, start=1):
        svc = _ensure_flags(coerce_types(raw_svc))
        print(f"\n--- Servicio {i} (entrada normalizada) ---")
        print(json.dumps(svc, indent=2, ensure_ascii=False))

        quote = engine.calculate_price(svc)
        print("\n► Resultado del cálculo")
        print(json.dumps(quote, indent=2, ensure_ascii=False))

        precio_num = quote.get("precio_final_num")
        if precio_num is None:
            raw = str(quote.get("precio_final_calculado", "0")).replace("€", "").replace(",", ".").strip()
            try:
                precio_num = float(raw)
            except:
                precio_num = 0.0
        total += precio_num

    if len(services) >= 3 and PACKAGE_DISCOUNT > 0:
        before = total
        total = round(total * (1.0 - PACKAGE_DISCOUNT), 2)
        print(f"\n💡 Descuento de paquete aplicado (-{int(PACKAGE_DISCOUNT*100)}%): {before:.2f}€ → {total:.2f}€")

    print("\n--- 4. Resumen Final ---")
    print(f"Servicios procesados: {len(services)}")
    print(f"PRECIO TOTAL GENERAL ESTIMADO: {total:.2f}€")

    resumen = ""
    try:
        resumen = build_whatsapp_summary_like(services, total, language)
        print("\n--- WhatsApp Preview ---")
        print(resumen)
    except Exception as e:
        print(f"ADVERTENCIA: No se pudo generar resumen WhatsApp: {e}")

    if args and args.out:
        output_data = {
            "services": services,
            "total_cotizacion_general": total,
            "whatsapp_summary": resumen,
            "language": language
        }
        try:
            if args.out_format == "json":
                with open(args.out, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=2, ensure_ascii=False)
                print(f"\nSalida JSON guardada en: {args.out}")
            else:
                with open(args.out, 'w', encoding='utf-8') as f:
                    f.write("# Cotización VTC 360\n\n")
                    f.write("## Resumen General\n")
                    f.write(f"Total de servicios: {len(services)}\n")
                    f.write(f"PRECIO TOTAL GENERAL ESTIMADO: {total:.2f}€\n\n")
                    f.write("## Detalles de Servicios\n")
                    for i, svc_out in enumerate(services, start=1):
                        f.write(f"### Servicio {i}\n```json\n{json.dumps(svc_out, indent=2, ensure_ascii=False)}\n```\n\n")
                    f.write("## Resumen WhatsApp\n")
                    f.write(f"```\n{resumen}\n```\n")
                print(f"\nSalida Markdown guardada en: {args.out}")
        except Exception as e:
            print(f"ERROR: No se pudo guardar la salida en el archivo {args.out}: {e}")

    # --- Guardar Sesión ---
    try:
        from runtime.session_manager import VTCSessionManager
        session = VTCSessionManager() # Path is now correct by default
        session.save_session({
            "command": "quote_calculation",
            "files": [
                args.in_json if hasattr(args, 'in_json') and args.in_json else "interactive",
                args.out if hasattr(args, 'out') and args.out else "console"
            ],
            "results": {
                "total": total,
                "services": len(services)
            },
            "context": {
                "client": "Sara Waisburd", # Hardcoded from previous context
                "timestamp": datetime.now().isoformat()
            }
        })
        print("\n✅ Sesión guardada. Puede cerrar VSCode y continuar después.")
    except Exception as e:
        print(f"\nADVERTENCIA: No se pudo guardar la sesión: {e}")

def read_request_text_interactive():
    print("Pega el itinerario completo. Escribe 'END' en una línea nueva para terminar.")
    lines = []
    while True:
        try:
            line = input()
        except EOFError:
            break
        if line.strip().upper() == "END":
            break
        lines.append(line)
    return "\n".join(lines).strip()

def main():
    """
    Función principal que orquesta el flujo de una cotización.
    """
    parser = argparse.ArgumentParser(description="VTC 360 — Procesador de itinerarios (Modo Local)")
    parser.add_argument("--file", help="Ruta a un archivo de texto para el extractor simple")
    parser.add_argument("--lang", help="Idioma preferido para el resumen (ES/FR/EN)", default=None)
    parser.add_argument("--out", help="Ruta al archivo de salida (ej. cotizacion.json)")
    parser.add_argument("--out-format", help="Formato de salida (json, md)", choices=["json", "md"], default="json")
    parser.add_argument("--in-json", help="Ruta a un archivo JSON con servicios ya extraídos (modo principal)")
    args = parser.parse_args()

    print("\n--- Sistema VTC 360 - Modo de Cálculo Local ---")

    in_json_data = None
    texto_solicitud = ""

    if args.in_json:
        try:
            with open(args.in_json, 'r', encoding='utf-8') as f:
                in_json_data = json.load(f)
            print(f"Fuente: JSON de entrada ({args.in_json})\n")
            _process_text("", preferred_lang=args.lang, args=args, in_json_data=in_json_data)
            return
        except Exception as e:
            print(f"ERROR: No se pudo cargar el JSON desde {args.in_json}: {e}")
            return

    if args.file:
        print(f"Fuente: archivo de texto ({args.file})\n")
        texto_solicitud = Path(args.file).read_text(encoding="utf-8").strip()
        _process_text(texto_solicitud, preferred_lang=args.lang, args=args, in_json_data=None)
        return

    print("Modo interactivo (extractor simple). Pega el itinerario y escribe END para terminar.\n")
    while True:
        texto_solicitud = read_request_text_interactive()
        if texto_solicitud.lower() == 'salir':
            print("Saliendo del sistema. ¡Hasta pronto!")
            return
        _process_text(texto_solicitud, preferred_lang=args.lang, args=args, in_json_data=None)

if __name__ == "__main__":
    main()
