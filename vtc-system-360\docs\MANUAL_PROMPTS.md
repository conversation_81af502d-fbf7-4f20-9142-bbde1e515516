# Manual de Prompts - Sistema VTC 360° v3.1
*Prompts Organizados por Orden de Uso en el Flujo Operativo*

## 📋 ÍNDICE DE PROMPTS (12 TOTAL) - ORDEN CRONOLÓGICO

---

## 🤖 **PROMPT MAESTRO: SECRETARIO INTELIGENTE** ⭐ **USAR SIEMPRE AL ABRIR VSCODE**

### 0. **`check-case-status.md`** - SECRETARIO INTELIGENTE IA 🧠
**Cuándo usar**: SIEMPRE al abrir VSCode o cambiar de caso
**Entrada**: Nombre del cliente o "¿dónde estoy?"
**Salida**: Estado actual completo y siguiente paso exacto
**Tiempo**: 30 segundos
**Siguiente paso**: El que te indique el sistema
```
@check-case-status.md
¿Dónde estoy con Sara Waisburd?
```

**🎯 FUNCIÓN ESPECIAL:**
- Lee archivo `runtime/case_tracking.json`
- Muestra progreso visual con barra
- Confirma estado actual contigo
- Te dice exactamente qué prompt usar siguiente
- Actualiza estado después de cada paso

---

## 🔄 **FASE 1: RECEPCIÓN Y CREACIÓN DE DEAL** 📧

### 1. **`crear-deal-hubspot.md`** - PRIMER CONTACTO ⭐ **INICIO**
**Cuándo usar**: Inmediatamente al recibir una nueva solicitud
**Entrada**: Email o mensaje inicial del cliente
**Salida**: Deal creado en HubSpot con datos estructurados
**Tiempo**: 2 minutos
**Siguiente paso**: Extraer itinerario detallado
```
@crear-deal-hubspot.md
Nueva solicitud de VIAMOSA para grupo 6 personas
```

---

## 🔄 **FASE 2: ANÁLISIS Y EXTRACCIÓN** 🤖

### 2. **`extract-itinerary.md`** - EXTRACCIÓN PRINCIPAL
**Cuándo usar**: Después de crear el deal, con itinerario detallado
**Entrada**: Texto completo con servicios (multilingüe)
**Salida**: JSON estructurado con todos los servicios
**Tiempo**: 3 minutos
**Siguiente paso**: Procesamiento Python automático
```
@extract-itinerary.md
[Pegar itinerario completo de agencia internacional]
```

### 3. **`extract-request-details.md`** - ANÁLISIS PROFUNDO
**Cuándo usar**: Para solicitudes complejas que necesitan más detalle
**Entrada**: Solicitudes con múltiples servicios o información confusa
**Salida**: Lista detallada de servicios individuales
**Tiempo**: 4 minutos
**Siguiente paso**: Continuar con cotización
```
@extract-request-details.md
Solicitud compleja con múltiples destinos y fechas
```

---

## 🔄 **FASE 3: COTIZACIÓN Y RESPUESTA** 💰

### 4. **`paris-elite-services-v7.md`** - COTIZACIÓN ESTÁNDAR
**Cuándo usar**: Para cotizaciones rápidas y directas (80% casos)
**Entrada**: Texto libre del cliente
**Salida**: Cotización formateada lista para enviar
**Tiempo**: 5 minutos
**Siguiente paso**: Enviar al cliente
```
@paris-elite-services-v7.md
Solicitud: Traslado CDG-París para 4 personas el 15 agosto
```

### 5. **`asistente-vip-experiencias.md`** - COTIZACIÓN PREMIUM 👑
**Cuándo usar**: Para clientes VIP o servicios exclusivos (15% casos)
**Entrada**: Solicitud de experiencias premium
**Salida**: Propuesta de lujo personalizada
**Tiempo**: 10 minutos
**Siguiente paso**: Seguimiento personalizado
```
@asistente-vip-experiencias.md
Cliente busca experiencia única París con acceso exclusivo
```

### 6. **`mensaje-cotizacion-final.md`** - RESPUESTA PROFESIONAL
**Cuándo usar**: Después del procesamiento Python, para respuesta final
**Entrada**: Datos de cotización procesados
**Salida**: Email profesional completo
**Tiempo**: 2 minutos
**Siguiente paso**: Actualizar deal en HubSpot
```
@mensaje-cotizacion-final.md
Generar respuesta para cotización Sara Waisburd procesada
```

---

## 🔄 **FASE 4: GESTIÓN COMERCIAL** 🎩

### 7. **`actualizar-deal-hubspot.md`** - GESTIÓN PIPELINE
**Cuándo usar**: En cada cambio de estado del deal
**Entrada**: Información de cambio de estado
**Salida**: Deal actualizado en pipeline correcto
**Tiempo**: 1 minuto
**Siguiente paso**: Según estado (seguimiento o operaciones)
```
@actualizar-deal-hubspot.md
Cliente confirmó servicios, actualizar a "Confirmé"
```

### 8. **`clarificacion-emails-cotizaciones.md`** - RESOLUCIÓN DUDAS
**Cuándo usar**: Cuando el cliente tiene preguntas o dudas (5% casos)
**Entrada**: Email con dudas del cliente
**Salida**: Respuesta clara y profesional
**Tiempo**: 3 minutos
**Siguiente paso**: Continuar seguimiento
```
@clarificacion-emails-cotizaciones.md
Cliente pregunta sobre horarios y tarifas nocturnas
```

---

## 🔄 **FASE 5: OPERACIONES Y DISPATCH** 🚗

### 9. **`generate-airtable-csv.md`** - EXPORTACIÓN OPERATIVA
**Cuándo usar**: Cuando el deal pasa a "Payé" (100%)
**Entrada**: Cotización confirmada y pagada
**Salida**: 3 archivos CSV para Airtable
**Tiempo**: 2 minutos
**Siguiente paso**: Importar a Airtable y asignar conductor
```
@generate-airtable-csv.md
Convertir cotización Sara Waisburd confirmada a CSV
```

### 10. **`dispatch-conductor.md`** - ÓRDENES DE TRABAJO
**Cuándo usar**: Después de importar a Airtable
**Entrada**: Servicios confirmados con conductor asignado
**Salida**: Instrucciones detalladas para conductor
**Tiempo**: 2 minutos
**Siguiente paso**: Ejecución del servicio
```
@dispatch-conductor.md
Generar dispatch para servicios Sara Waisburd 11-14 agosto
```

---

## 🔄 **FASE 6: GESTIÓN EMPRESARIAL** ⚙️

### 11. **`sistema-operativo-empresarial.md`** - WORKFLOWS AVANZADOS
**Cuándo usar**: Para gestión empresarial y optimización de procesos
**Entrada**: Datos operativos y comerciales
**Salida**: Análisis y recomendaciones de mejora
**Tiempo**: 15 minutos
**Siguiente paso**: Implementar optimizaciones
```
@sistema-operativo-empresarial.md
¿Cómo optimizar la gestión de 3 servicios simultáneos?
```

---

## 🎯 **FLUJOS TÍPICOS DE USO**

### **FLUJO COMPLETO ESTÁNDAR CON SECRETARIO IA** (80% de casos)
```
0. check-case-status.md (SIEMPRE AL ABRIR VSCODE)
1. crear-deal-hubspot.md
2. extract-itinerary.md
3. [Procesamiento Python automático]
4. mensaje-cotizacion-final.md
5. actualizar-deal-hubspot.md (Cotisation Envoyée)
6. [Cliente confirma]
7. actualizar-deal-hubspot.md (Confirmé → Payé)
8. generate-airtable-csv.md
9. dispatch-conductor.md

🤖 ENTRE CADA PASO: check-case-status.md para confirmar progreso
```

### **FLUJO VIP PREMIUM** (15% de casos)
```
1. crear-deal-hubspot.md
2. asistente-vip-experiencias.md
3. [Personalización manual]
4. mensaje-cotizacion-final.md
5. [Seguimiento personalizado]
6. actualizar-deal-hubspot.md
7. generate-airtable-csv.md
8. dispatch-conductor.md
```

### **FLUJO ACLARACIÓN** (5% de casos)
```
1. clarificacion-emails-cotizaciones.md
2. [Si necesita nueva cotización: volver al flujo principal]
3. actualizar-deal-hubspot.md
```

---

## 📋 **GUÍA RÁPIDA DE DECISIÓN**

| Situación | Prompt a Usar | Tiempo | Frecuencia |
|-----------|---------------|---------|------------|
| 🤖 **Abrir VSCode** | `check-case-status.md` | 30 seg | 100% |
| 🤖 **¿Dónde estoy?** | `check-case-status.md` | 30 seg | Siempre |
| 📧 **Nueva solicitud** | `crear-deal-hubspot.md` | 2 min | 100% |
| 📄 **Itinerario complejo** | `extract-itinerary.md` | 3 min | 80% |
| 🤔 **Solicitud confusa** | `extract-request-details.md` | 4 min | 20% |
| ⚡ **Cotización rápida** | `paris-elite-services-v7.md` | 5 min | 80% |
| 👑 **Cliente VIP** | `asistente-vip-experiencias.md` | 10 min | 15% |
| 📧 **Respuesta final** | `mensaje-cotizacion-final.md` | 2 min | 95% |
| 🎩 **Cambio de estado** | `actualizar-deal-hubspot.md` | 1 min | 100% |
| ❓ **Dudas cliente** | `clarificacion-emails-cotizaciones.md` | 3 min | 5% |
| 📊 **Servicio confirmado** | `generate-airtable-csv.md` | 2 min | 60% |
| 🚗 **Asignar conductor** | `dispatch-conductor.md` | 2 min | 60% |
| ⚙️ **Optimización** | `sistema-operativo-empresarial.md` | 15 min | Semanal |

---

## ⚡ **ATAJOS Y OPTIMIZACIONES**

### **PARA SOLICITUDES SIMPLES** (30% casos)
```
crear-deal-hubspot.md → paris-elite-services-v7.md → actualizar-deal-hubspot.md
```

### **PARA CLIENTES RECURRENTES** (20% casos)
```
asistente-vip-experiencias.md → mensaje-cotizacion-final.md → generate-airtable-csv.md
```

### **PARA URGENCIAS** (10% casos)
```
extract-itinerary.md → Python → mensaje-cotizacion-final.md (directo)
```

---

## 🔄 **DEPENDENCIAS ENTRE PROMPTS**

**SECUENCIA OBLIGATORIA:**
1. **SIEMPRE** empezar con `crear-deal-hubspot.md`
2. **ANÁLISIS** con `extract-itinerary.md` o `extract-request-details.md`
3. **COTIZACIÓN** con `paris-elite-services-v7.md` o `asistente-vip-experiencias.md`
4. **RESPUESTA** con `mensaje-cotizacion-final.md`
5. **SEGUIMIENTO** con `actualizar-deal-hubspot.md`
6. **OPERACIONES** con `generate-airtable-csv.md` → `dispatch-conductor.md`

**PROMPTS OPCIONALES:**
- `clarificacion-emails-cotizaciones.md` - Solo si hay dudas
- `sistema-operativo-empresarial.md` - Para optimización periódica

---

**🎯 RESULTADO**: Manual completamente reorganizado por orden cronológico de uso, eliminando confusión y maximizando la eficiencia operativa del Sistema VTC 360° v3.1.
