import json
import unittest
from pathlib import Path

# Asumimos que el motor y el caché se cargarán o simularán aquí
# Por ahora, cargaremos el JSON directamente para probar la lógica de datos.

class TestVTCCacheData(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        """Carga el caché una vez para todas las pruebas."""
        cache_path = Path(__file__).parent.parent / "runtime/cached_context.json"
        if not cache_path.exists():
            raise FileNotFoundError("El archivo cached_context.json no ha sido generado. Ejecuta init_cache.py --build")
        with open(cache_path, 'r', encoding='utf-8') as f:
            cls.cache = json.load(f)

    def test_precios_aeropuerto_dia(self):
        """Verifica el precio de un traslado de día desde CDG."""
        precio_cdg_dia = self.cache['data']['tarifas']['cdg']['dia']
        self.assertEqual(precio_cdg_dia, 120)

    def test_precios_aeropuerto_noche(self):
        """Verifica el precio de un traslado de noche desde ORY."""
        precio_ory_noche = self.cache['data']['tarifas']['ory']['noche']
        self.assertEqual(precio_ory_noche, 115)

    def test_recargo_urgencia(self):
        """Verifica el recargo por urgencia."""
        recargo = self.cache['data']['tarifas']['recargo_urgente_2h']
        self.assertEqual(recargo, 35)

    def test_festivos_cargados(self):
        """Verifica que los festivos se hayan cargado correctamente."""
        festivos = self.cache['data']['festivos']
        self.assertEqual(len(festivos), 11)
        # Verificar un festivo específico
        fete_nationale = next((f for f in festivos if f['nombre'] == "Fête Nationale"), None)
        self.assertIsNotNone(fete_nationale)
        self.assertEqual(fete_nationale['fecha'], "2025-07-14")

    def test_vehiculos_disponibles(self):
        """Verifica la cantidad y tipo de vehículos."""
        vehiculos = self.cache['data']['vehiculos']
        self.assertEqual(len(vehiculos), 3)
        modelos = [v['modelo'] for v in vehiculos]
        self.assertIn("Mercedes Clase V", modelos)

if __name__ == '__main__':
    unittest.main(verbosity=2)