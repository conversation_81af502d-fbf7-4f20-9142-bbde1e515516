# Sistema VTC 360° v3.2 - Guía Rápida con Secretario Inteligente IA

## 🏗️ ARQUITECTURA DEL SISTEMA

```
SISTEMA VTC 360° v3.2 CON SECRETARIO IA
├── 🤖 SECRETARIO IA - Seguimiento automático de casos
├── 🎩 HUBSPOT - Gestión Comercial (Pipeline 4 etapas)
├── 📝 PROMPTS - 12 prompts especializados
├── 🐍 PYTHON - Motor Cálculo (12 scripts)
├── 📊 AIRTABLE - Operación y dispatch
└── 📱 WHATSAPP - Comunicación cliente
```

## 🚀 INSTALACIÓN RÁPIDA

```bash
# 1. <PERSON>lona el proyecto
git clone <URL-del-repo>
cd vtc-system-360

# 2. Instala dependencias Python
pip install -r requirements.txt

# 3. Configuración opcional (.env)
DEEPSEEK_API_KEY=tu_api_key_aqui
GEMINI_API_KEY=tu_api_key_aqui
EXTRACTOR=gemini
PACKAGE_DISCOUNT=0.05
```

## 🤖 SECRETARIO INTELIGENTE IA - COMANDO MAESTRO

### **⭐ SIEMPRE AL ABRIR VSCODE:**
```
@check-case-status.md
¿dónde estoy?
```
**Resultado**: La IA lee tu estado actual y te guía paso a paso desde donde te quedaste.

### **COMANDOS PRINCIPALES DEL SECRETARIO IA**
```
# Estado actual
@check-case-status.md
¿dónde estoy?

# Caso específico
@check-case-status.md
estado Sara Waisburd

# Marcar paso completado
@check-case-status.md
marcar paso 3 completado

# Cambiar de caso
@check-case-status.md
cambiar a caso [cliente]

# Crear nuevo caso
@check-case-status.md
crear nuevo caso Sara Waisburd
```

### **COMANDOS PYTHON OPCIONALES**
```bash
# Ver estado actual
python runtime/case_manager.py status

# Crear nuevo caso
python runtime/case_manager.py create "Sara Waisburd" "2025-08-11_2025-08-14"

# Marcar paso completado
python runtime/case_manager.py complete sara_waisburd_2025_08 step_1_create_deal

# Listar todos los casos
python runtime/case_manager.py list
```

### **🔄 FLUJO GUIADO AUTOMÁTICO**
1. **Abres VSCode** → `@check-case-status.md ¿dónde estoy?`
2. **IA lee estado** → Te muestra progreso y siguiente paso
3. **Ejecutas prompt** → El que te indica la IA
4. **Marcas completado** → `@check-case-status.md marcar paso X completado`
5. **IA te guía** → Al siguiente paso automáticamente
6. **Repites** → Hasta completar todos los pasos

### **📊 LOS 8 PASOS RASTREADOS**
1. **Crear Deal HubSpot** → `crear-deal-hubspot.md`
2. **Procesar Itinerario** → `extract-itinerary.md`
3. **Enviar Cotización** → `mensaje-cotizacion-final.md`
4. **Confirmación Cliente** → Verificar respuesta
5. **Actualizar Deal Confirmado** → `actualizar-deal-hubspot.md`
6. **Confirmar Pago** → `actualizar-deal-hubspot.md`
7. **Generar CSV Operativo** → `generate-airtable-csv.md`
8. **Dispatch Conductor** → `dispatch-conductor.md`

## 🔄 FLUJO COMPLETO GUIADO POR IA

### **CASO TÍPICO: SARA WAISBURD**

#### **SESIÓN 1 - CREAR DEAL**
```
1. Abres VSCode
2. @check-case-status.md ¿dónde estoy?
3. IA: "No encuentro caso Sara Waisburd. ¿Crear nuevo?"
4. Tú: "Sí"
5. IA: "Ejecuta @crear-deal-hubspot.md"
6. Ejecutas prompt y creas deal
7. @check-case-status.md marcar paso 1 completado
8. IA: "Paso 1 ✅. Siguiente: @extract-itinerary.md"
```

#### **SESIÓN 2 - PROCESAR ITINERARIO**
```
1. Abres VSCode (al día siguiente)
2. @check-case-status.md ¿dónde estoy?
3. IA: "Sara Waisburd - Paso 2: Procesar itinerario"
4. IA: "Ejecuta @extract-itinerary.md"
5. Ejecutas prompt con itinerario completo
6. Motor Python calcula precios automáticamente
7. @check-case-status.md marcar paso 2 completado
8. IA: "Paso 2 ✅. Siguiente: @mensaje-cotizacion-final.md"
```

#### **SESIÓN 3 - ENVIAR COTIZACIÓN**
```
1. Abres VSCode
2. @check-case-status.md ¿dónde estoy?
3. IA: "Sara Waisburd - Paso 3: Enviar cotización"
4. IA: "Ejecuta @mensaje-cotizacion-final.md"
5. Ejecutas prompt y generas email profesional
6. Envías email al cliente
7. @check-case-status.md marcar paso 3 completado
8. IA: "Paso 3 ✅. Esperar confirmación cliente"
```

#### **SESIÓN 4 - CONFIRMACIÓN Y PAGO**
```
1. Cliente confirma por email/WhatsApp
2. @check-case-status.md ¿dónde estoy?
3. IA: "Sara Waisburd - Paso 4: Cliente confirmó?"
4. Tú: "Sí, confirmó"
5. IA: "Ejecuta @actualizar-deal-hubspot.md"
6. Actualizas deal a "Confirmé (En attente de paiement)"
7. Cliente paga
8. @actualizar-deal-hubspot.md → Deal a "Payé"
9. IA: "Activar operaciones: @generate-airtable-csv.md"
```

#### **SESIÓN 5 - OPERACIONES**
```
1. @generate-airtable-csv.md → Generar 3 CSV
2. Importar CSV a Airtable
3. @dispatch-conductor.md → Órdenes de trabajo
4. IA: "Caso completado ✅"
```

### **FLUJO TÉCNICO SIN API (OPCIONAL)**

Si prefieres usar Gemini en VSCode sin APIs:

#### **Paso 1: Extraer con Gemini en VSCode**
1. Abre `02-prompts/extract-itinerary.md`
2. Pega contenido en chat Gemini VSCode
3. Pega itinerario del cliente
4. Gemini devuelve JSON estructurado

#### **Paso 2: Guardar y Procesar**
1. Guarda respuesta en `extract.json`
2. Ejecuta: `python app.py --in-json extract.json --skip-llm --out cotizacion.json`
3. Continúa con flujo normal

## 🎯 COMANDOS ESENCIALES

### **COMANDO MAESTRO (SIEMPRE AL ABRIR VSCODE)**
```
@check-case-status.md
¿dónde estoy?
```

### **COMANDOS PYTHON ÚTILES**
```bash
# Procesar con motor local
python app.py --in-json extract.json --skip-llm --out cotizacion.json

# Ejecutar tests
python -m pytest tests/

# Ver estado de casos
python runtime/case_manager.py status

# Procesar reserva completa
python procesar_reserva.bat
```

### **TAREAS VSCODE**
- `Ctrl+Shift+B` → **Run tests**
- `Ctrl+Shift+P` → **Tasks: Run Task**

## 🚨 TROUBLESHOOTING

### **PROBLEMAS COMUNES**
- **ImportError**: `pip install -r requirements.txt`
- **API Key inválida**: Revisar archivo `.env`
- **Archivo no encontrado**: Verificar rutas relativas
- **JSON malformado**: Validar salida de prompts

### **COMANDOS DE DIAGNÓSTICO**
```bash
# Verificar instalación
python --version
pip list | grep -E "(openai|google-generativeai)"

# Verificar configuración
python -c "import os; print(os.getenv('EXTRACTOR', 'No configurado'))"

# Ejecutar tests
python -m pytest tests/ -v
```

### **ARCHIVOS DE LOG**
- `runtime/case_tracking.json` - Estado de casos
- `03-outputs/` - Archivos generados
- `04-analytics/` - Reportes del sistema

## 📚 DOCUMENTACIÓN COMPLETA

### **MANUALES PRINCIPALES**
- `docs/GUIA_SECRETARIO_INTELIGENTE.md` - Guía completa del Secretario IA
- `docs/MANUAL_PROMPTS.md` - 12 prompts especializados
- `docs/SISTEMA_OPERATIVO_EMPRENDEDOR.md` - Filosofía del sistema
- `docs/ANALISIS_MERCADO_TURISMO_LUJO_PARIS.md` - Contexto estratégico

### **CONFIGURACIÓN**
- `00-config/tarifas.md` - Precios y reglas de negocio
- `00-config/festivos-fr.md` - Calendario festivos Francia
- `docs/INTEGRACION_HUBSPOT.md` - Setup comercial

### **NAVEGACIÓN**
- `docs/INDICE_DOCUMENTACION.md` - Índice completo
- `README.md` - Documentación técnica principal

## 🎉 RESULTADO FINAL

**✅ SISTEMA VTC 360° v3.2 - SECRETARIO INTELIGENTE IA**

- **🤖 Nunca te pierdes** - El sistema siempre sabe dónde estás
- **⚡ Máxima eficiencia** - Guía paso a paso automática
- **🎯 Calidad garantizada** - Todos los pasos se completan
- **🔄 Memoria persistente** - Funciona entre sesiones VSCode
- **📊 Múltiples casos** - Gestiona varios clientes simultáneamente

**¡El futuro de la gestión empresarial está aquí!** 🚀
