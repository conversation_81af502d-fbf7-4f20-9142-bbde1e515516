#!/usr/bin/env python3
"""
VTC 360° Cache Builder
Genera cached_context.json desde archivos markdown
"""
import json
import hashlib
import os
import re
from datetime import datetime
from pathlib import Path

class VTCCacheBuilder:
    def __init__(self, root_path="."):
        self.root = Path(root_path)
        self.cache = {
            "version": "1.2.0",
            "generated_at": datetime.now().isoformat(),
            "checksum": "",
            "data": {
                "tarifas": {},
                "festivos": [],
                "zonas": {},
                "vehiculos": [],
                "reglas": {},
                "plantillas": {},
                "modo_evento": {}
            }
        }

    def parse_tarifas(self):
        """Parsea tarifas.md y extrae precios"""
        tarifa_file = self.root / "00-config" / "tarifas.md"
        if not tarifa_file.exists():
            return
        content = tarifa_file.read_text(encoding='utf-8')

        # Usar re.DOTALL para que '.' coincida con saltos de línea
        # Extraer tarifas CDG
        cdg_pattern = r"CDG \(<PERSON>\).*?París intramuros:\s*(\d+)€\s*día\s*/\s*(\d+)€\s*noche"
        cdg_match = re.search(cdg_pattern, content, re.DOTALL)
        if cdg_match:
            self.cache["data"]["tarifas"]["cdg"] = {
                "dia": int(cdg_match.group(1)),
                "noche": int(cdg_match.group(2))
            }

        # Extraer tarifas ORY
        ory_pattern = r"ORY \(Orly\).*?París intramuros:\s*(\d+)€\s*día\s*/\s*(\d+)€\s*noche"
        ory_match = re.search(ory_pattern, content, re.DOTALL)
        if ory_match:
            self.cache["data"]["tarifas"]["ory"] = {
                "dia": int(ory_match.group(1)),
                "noche": int(ory_match.group(2))
            }

        # Extraer disposición
        dispo_pattern = r"Tarifas base.*?:\s*(\d+)€/hora"
        dispo_match = re.search(dispo_pattern, content, re.DOTALL)
        if dispo_match:
            self.cache["data"]["tarifas"]["disposicion_hora"] = int(dispo_match.group(1))

        # Extraer recargos
        urgente_pattern = r"Urgencia.*<2h:\s*\+(\d+)%"
        urgente_match = re.search(urgente_pattern, content, re.DOTALL)
        if urgente_match:
            self.cache["data"]["tarifas"]["recargo_urgente_2h"] = int(urgente_match.group(1))

    def parse_festivos(self):
        """Parsea festivos-fr.md"""
        festivos_file = self.root / "00-config" / "festivos-fr.md"
        if not festivos_file.exists():
            return
        content = festivos_file.read_text(encoding='utf-8')

        # Extraer fechas formato DD/MM
        fecha_pattern = r"-\s*(\d{2}/\d{2})\s*-\s*(.+)"
        festivos = re.findall(fecha_pattern, content)
        for fecha, nombre in festivos:
            dia, mes = fecha.split('/')
            self.cache["data"]["festivos"].append({
                "fecha": f"2025-{mes}-{dia}",
                "nombre": nombre.strip(),
                "recargo": 15
            })

    def parse_vehiculos(self):
        """Define flota disponible"""
        self.cache["data"]["vehiculos"] = [
            {
                "modelo": "Mercedes Clase V",
                "capacidad_pax": 7,
                "capacidad_maletas": 7,
                "tarifa_base": "standard"
            },
            {
                "modelo": "Mercedes Clase S",
                "capacidad_pax": 3,
                "capacidad_maletas": 3,
                "tarifa_base": "premium",
                "multiplicador": 1.5
            },
            {
                "modelo": "Mercedes Sprinter",
                "capacidad_pax": 20,
                "capacidad_maletas": 20,
                "tarifa_base": "grupo",
                "multiplicador": 1.8
            }
        ]

    def parse_zonas(self):
        """Define zonas y sus características"""
        self.cache["data"]["zonas"] = {
            "cdg": {
                "nombre": "Aéroport Charles de Gaulle",
                "codigo": "CDG",
                "keywords": ["cdg", "charles de gaulle", "roissy", "terminal"],
                "tipo": "aeropuerto"
            },
            "ory": {
                "nombre": "Aéroport Orly",
                "codigo": "ORY",
                "keywords": ["orly", "ory"],
                "tipo": "aeropuerto"
            },
            "paris": {
                "nombre": "Paris Intramuros",
                "codigo": "PARIS",
                "keywords": ["paris", "75001", "75002", "tour eiffel", "champs"],
                "tipo": "ciudad"
            },
            "la_defense": {
                "nombre": "La Défense",
                "codigo": "DEF",
                "keywords": ["defense", "défense", "puteaux", "courbevoie"],
                "tipo": "business",
                "suplemento": 20
            }
        }

    def parse_reglas(self):
        """Define reglas de negocio"""
        self.cache["data"]["reglas"] = {
            "horario_nocturno": {
                "inicio": "22:00",
                "fin": "06:00",
                "recargo_porcentaje": 17
            },
            "espera_aeropuerto": {
                "minutos_gratis": 45,
                "tarifa_adicional": 15,
                "intervalo": 15
            },
            "grupo_grande": {
                "umbral": 7,
                "accion": "verificar_vehiculos"
            },
            "urgencia": {
                "niveles": [
                    {"horas": 6, "recargo": 20},
                    {"horas": 2, "recargo": 35}
                ]
            },
            "descuentos": {
                "orden_aplicacion": [
                    "cliente_recurrente",
                    "pago_anticipado",
                    "reserva_anticipada"
                ]
            }
        }

    def parse_modo_evento(self):
        """Verifica si hay evento activo"""
        evento_file = self.root / "00-config" / "modo-evento.md"
        if not evento_file.exists():
            self.cache["data"]["modo_evento"] = {"activo": False}
            return
        content = evento_file.read_text(encoding='utf-8')

        # Verificar si está activo
        if "[ACTIVO]" in content:
            self.cache["data"]["modo_evento"] = {
                "activo": True,
                "nombre": "Fashion Week",
                "multiplicador": 1.5,
                "fecha_inicio": "2025-03-03",
                "fecha_fin": "2025-03-11"
            }
        else:
            self.cache["data"]["modo_evento"] = {"activo": False}

    def compute_checksum(self):
        """Genera checksum del contenido"""
        content = json.dumps(self.cache["data"], sort_keys=True)
        return hashlib.md5(content.encode()).hexdigest()

    def build(self):
        """Construye el caché completo"""
        print("Construyendo cache VTC 360...")
        # Parsear todos los archivos
        self.parse_tarifas()
        self.parse_festivos()
        self.parse_vehiculos()
        self.parse_zonas()
        self.parse_reglas()
        self.parse_modo_evento()
        # Generar checksum
        self.cache["checksum"] = self.compute_checksum()
        # Guardar caché
        output_path = self.root / "runtime" / "cached_context.json"
        output_path.parent.mkdir(exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, indent=2, ensure_ascii=False)
        print(f"Cache generado: {output_path}")
        print(f"Checksum: {self.cache['checksum'][:8]}...")
        print(f"Festivos cargados: {len(self.cache['data']['festivos'])}")
        print(f"Vehiculos disponibles: {len(self.cache['data']['vehiculos'])}")
        return self.cache

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='VTC 360° Cache Builder')
    parser.add_argument('--root', default='.', help='Directorio raíz del proyecto')
    parser.add_argument('--validate', action='store_true', help='Validar caché existente')
    parser.add_argument('--build', action='store_true', help='Construir o reconstruir el caché')
    args = parser.parse_args()
    
    # Ajustar el directorio raíz si se ejecuta desde otro lugar
    if args.root == '.':
        script_path = Path(__file__).resolve()
        # Asumimos que el script está en 05-runtime y la raíz es el padre del padre
        project_root = script_path.parent.parent
    else:
        project_root = Path(args.root)

    builder = VTCCacheBuilder(project_root)

    if args.validate:
        # Modo validación
        cache_file = project_root / "runtime" / "cached_context.json"
        if cache_file.exists():
            with open(cache_file, 'r', encoding='utf-8') as f:
                existing = json.load(f)
            builder.parse_tarifas()
            builder.parse_festivos()
            builder.parse_vehiculos()
            builder.parse_zonas()
            builder.parse_reglas()
            builder.parse_modo_evento()
            new_checksum = builder.compute_checksum()
            if new_checksum == existing.get("checksum"):
                print("Cache valido y actualizado")
            else:
                print("ADVERTENCIA: Cache desactualizado, regenerar con --build")
        else:
            print("ERROR: No existe cache, generar con --build")
    
    if args.build:
        # Modo construcción
        builder.build()
