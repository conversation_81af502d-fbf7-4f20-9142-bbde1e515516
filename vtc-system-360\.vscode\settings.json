{"vtc-system": {"version": "2.0.1", "mode": "LOCAL_ONLY", "ai_engine": "gemini_local", "deprecated_features": {"multi_llm_orchestration": false, "api_calls": false, "external_validation": false}, "new_features": {"local_processing": true, "zero_latency": true, "offline_capable": true, "privacy_first": true}, "claude_opus_integration": {"role": "strategic_supervisor", "frequency": "weekly", "access": "read_only", "reports_path": "/04-analytics/claude-reports/"}, "session_persistence": true, "auto_restore": true, "session_timeout_hours": 24, "startup_commands": ["python vtc-system-360/runtime/session_manager.py --restore", "echo 'Sesión restaurada'"], "files.watcherExclude": {"**/vtc-system-360/runtime/sessions/**": false}}}