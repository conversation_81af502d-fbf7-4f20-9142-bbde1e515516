# SISTEMA MULTI-PROMPTS PARIS ELITE SERVICES

## 📋 DESCRIPCIÓN
Sistema de prompts especializados para generar cotizaciones profesionales y análisis de rentabilidad para Paris Elite Services.

## 📁 ESTRUCTURA DE ARCHIVOS

```
.augment/
├── README.md                    # Este archivo
└── rules/
    ├── sistema-principal.md     # Configuración principal del agente
    ├── cotizaciones-base.md     # Comportamiento y estructura base
    ├── precios-servicios.md     # Tarifas y servicios disponibles
    ├── formato-respuesta.md     # Plantillas de cotización
    ├── datos-empresa.md         # Información corporativa
    ├── analisis-rentabilidad.md # Cálculos de costos y márgenes
    └── gestion-clientes.md      # Perfiles y estrategias de clientes
```

## 🚀 CÓMO USAR EL SISTEMA

### MÉTODO 1: Activación Automática (Recomendado)
El sistema se activa automáticamente cuando VS Code detecta la carpeta `.augment/rules/`. Todos los archivos se cargan como contexto para el agente.

### MÉTODO 2: Activación Manual
Si necesitas activar manualmente:
1. Abre VS Code en el directorio del proyecto
2. Usa Ctrl+Shift+P → "Augment: Load Rules"
3. Selecciona la carpeta `.augment/rules/`

## 📝 TIPOS DE CONSULTAS SOPORTADAS

### COTIZACIONES ESTÁNDAR
```
"Necesito cotizar un traslado del aeropuerto a Manuel Antonio para 4 personas el 15 de marzo"
```

### ANÁLISIS DE RENTABILIDAD
```
"Analiza la rentabilidad de un tour a Monteverde con van ejecutiva por $280"
```

### GESTIÓN DE CLIENTES
```
"Federico de Federico Tours necesita precios para un grupo de 8 personas a Arenal"
```

### SERVICIOS ESPECIALES
```
"Cotiza un servicio nocturno de emergencia del hospital a Cartago"
```

## 🎯 CARACTERÍSTICAS PRINCIPALES

### ✅ COTIZACIONES INTELIGENTES
- Cálculo automático de precios según distancia y vehículo
- Aplicación de descuentos según tipo de cliente
- Asignación inteligente de choferes
- Formato profesional personalizado

### ✅ ANÁLISIS DE RENTABILIDAD
- Cálculo de costos directos e indirectos
- Márgenes objetivo por tipo de servicio
- Alertas de rentabilidad baja
- Optimización de rutas y recursos

### ✅ GESTIÓN DE CLIENTES
- Clasificación automática por tiers
- Comunicación personalizada por tipo
- Seguimiento post-servicio
- Estrategias de retención

### ✅ OPERACIONES OPTIMIZADAS
- Asignación eficiente de recursos
- Manejo de casos especiales
- Escalación automática cuando necesario
- Métricas de desempeño

## 🔧 CONFIGURACIÓN PERSONALIZADA

### ACTUALIZAR PRECIOS
Edita `precios-servicios.md` con las nuevas tarifas:
```markdown
| Ruta | Distancia | Sedán | SUV | Van | Bus |
|------|-----------|-------|-----|-----|-----|
| Nueva Ruta | 100km | $120 | $150 | $200 | $280 |
```

### AGREGAR NUEVOS CHOFERES
Edita `datos-empresa.md` en la sección de choferes:
```markdown
### CHOFER NUEVO
- **Nombre Apellido**
- Experiencia: X años
- Idiomas: Español, Inglés
- Especialidad: Tours/Ejecutivo
- Contacto: +506 XXXX-XXXX
```

### MODIFICAR CLIENTES
Edita `gestion-clientes.md` para agregar nuevos perfiles:
```markdown
### NUEVO CLIENTE
- **Contacto**: Nombre Contacto
- **Relación**: X años
- **Volumen**: $X/mes
- **Especialidad**: Tipo de servicios
- **Comunicación**: Formal/Informal
- **Preferencias**: Vehículos preferidos
- **Pago**: Condiciones de pago
```

## 📊 MÉTRICAS Y REPORTES

### INDICADORES AUTOMÁTICOS
- Margen de rentabilidad por servicio
- Clasificación de cliente automática
- Alertas de precios por debajo del mínimo
- Sugerencias de optimización

### REPORTES DISPONIBLES
- Análisis de rentabilidad por ruta
- Desempeño por tipo de cliente
- Utilización de flota
- Satisfacción del cliente

## 🚨 ALERTAS Y VALIDACIONES

### ALERTAS ROJAS (Rechazar)
- Margen < 25%
- Precio < mínimo establecido
- Cliente con historial de pagos

### ALERTAS AMARILLAS (Evaluar)
- Margen 25-30%
- Cliente nuevo sin referencias
- Servicios en horarios complicados

### ALERTAS VERDES (Aceptar)
- Margen > 35%
- Cliente frecuente
- Servicios estándar

## 🔄 MANTENIMIENTO

### ACTUALIZACIONES REGULARES
- **Semanal**: Disponibilidad de choferes
- **Mensual**: Precios y tarifas
- **Trimestral**: Análisis de competencia
- **Anual**: Revisión completa del sistema

### BACKUP DE CONFIGURACIÓN
Mantén copias de seguridad de la carpeta `.augment/rules/` para preservar configuraciones personalizadas.

## 📞 SOPORTE

### PROBLEMAS COMUNES
1. **El sistema no responde**: Verifica que la carpeta `.augment/rules/` esté en la raíz del proyecto
2. **Precios incorrectos**: Revisa `precios-servicios.md` para tarifas actualizadas
3. **Formato incorrecto**: Verifica `formato-respuesta.md` para plantillas

### CONTACTO TÉCNICO
Para soporte técnico o personalizaciones adicionales, contacta al administrador del sistema.

---

**Versión**: 1.0  
**Última actualización**: Enero 2025  
**Desarrollado para**: Paris Elite Services - Boris Porras